variables:
  - name: workingDirectory # This variable allows managing different configurations in the same repository
    value: $(System.DefaultWorkingDirectory)
  - name: workingDirectoryTerraform # This variable allows managing different configurations in the same repository
    value: $(System.DefaultWorkingDirectory)/iac/terraform
  - name: workingDirectoryJava
    value: $(System.DefaultWorkingDirectory)/app
  - name: containerAppRgName
    value: "rg-aita-backend"
  - name: containerAppName
    value: "ca-aita-backend"
  - name: containerRegistryUrl
    value: "craita.azurecr.io"
  - name: imageName
    value: "aita-backend"
  - name: mavenVersion
    value: '3.9.0'
  - name: javaVersion
    value: '21'
  - group: common-variables
  - group: backend

trigger:
  batch: true
  branches:
    include:
      - main

pool:
  vmImage: 'ubuntu-latest'

stages:
  - stage: ExecuteTerraform
    displayName: Execute Terraform
    jobs:
      - job: TerraformPlan
        displayName: Terraform plan
        continueOnError: false
        steps:
          - task: TerraformInstaller@1
            displayName: Install Terraform
            inputs:
              terraformVersion: $(terraformVersion)

          - task: TerraformTaskV4@4
            displayName: Initialize Terraform
            inputs:
              provider: 'azurerm'
              command: 'init'
              workingDirectory: $(workingDirectoryTerraform)
              backendServiceArm: $(serviceConnection)
              backendAzureRmResourceGroupName: $(tfBackendStorageAccountResourceGroup)
              backendAzureRmStorageAccountName: $(tfBackendStorageAccountName)
              backendAzureRmContainerName: $(tfBackendStorageAccountContainerName)
              backendAzureRmKey: $(tfBackendStorageAccountKey)

          - task: TerraformTaskV4@4
            displayName: Validate Terraform configuration
            inputs:
              provider: 'azurerm'
              command: 'validate'
              workingDirectory: $(workingDirectoryTerraform)
              environmentServiceNameAzureRM: $(serviceConnection)

          - task: TerraformTaskV4@4
            name: terraformPlan
            displayName: Create Terraform plan
            inputs:
              provider: 'azurerm'
              command: 'plan'
              commandOptions: '-out plan.tfplan'
              workingDirectory: $(workingDirectoryTerraform)
              environmentServiceNameAzureRM: $(serviceConnection)

          # Plan must be saved between job execution since they might take place on different agents
          - task: PublishPipelineArtifact@1
            inputs:
              targetPath: $(workingDirectoryTerraform)/plan.tfplan
              artifactName: plan.tfplan

        # ManualValidation task is used to allow reviewing the Terraform plan before applying it. It must be
        # placed in a separate job since it's an agent-less job.
      - job: Validate
        displayName: Manual plan validation
        condition: and(succeeded(), eq(dependencies.TerraformPlan.outputs['terraformPlan.changesPresent'], 'true'))
        dependsOn: TerraformPlan
        continueOnError: false
        pool: server
        steps:
          - task: ManualValidation@0
            displayName: Validate Terraform plan
            timeoutInMinutes: 60 # task times out in 1 day
            inputs:
              instructions: 'Please validate the Terraform plan and resume afterwards'

      - job: TerraformApply
        displayName: Terraform apply
        condition: and(succeeded(), eq(dependencies.TerraformPlan.outputs['terraformPlan.changesPresent'], 'true'))
        dependsOn: Validate
        continueOnError: false
        steps:
          - task: DownloadPipelineArtifact@2
            inputs:
              path: $(workingDirectoryTerraform)
              artifact: plan.tfplan

          - task: TerraformInstaller@1
            displayName: Install Terraform
            inputs:
              terraformVersion: $(terraformVersion)

          - task: TerraformTaskV4@4
            displayName: Initialize Terraform
            inputs:
              provider: 'azurerm'
              command: 'init'
              workingDirectory: $(workingDirectoryTerraform)
              backendServiceArm: $(serviceConnection)
              backendAzureRmResourceGroupName: $(tfBackendStorageAccountResourceGroup)
              backendAzureRmStorageAccountName: $(tfBackendStorageAccountName)
              backendAzureRmContainerName: $(tfBackendStorageAccountContainerName)
              backendAzureRmKey: $(tfBackendStorageAccountKey)

          - task: TerraformTaskV4@4
            displayName: Apply Terraform plan
            inputs:
              provider: 'azurerm'
              command: 'apply'
              commandOptions: '-auto-approve plan.tfplan'
              workingDirectory: $(workingDirectoryTerraform)
              environmentServiceNameAzureRM: $(serviceConnection)

  - stage: Build
    displayName: Build and Test
    jobs:
      - job: Build
        displayName: Build and Test Application
        steps:
          # This necessary to provide credentials for the next task
          - checkout: self
            persistCredentials: true

          - bash: |
              set -e
              # Assemble artifact version number
              SHORT_COMMIT_HASH=`git rev-parse --short=7 HEAD`
              ARTIFACT_VERSION=`grep "<revision>" $(workingDirectoryJava)/pom.xml | cut -d ">" -f 2 | cut -d "<" -f 1`
              ARTIFACT_LONG_VERSION="${ARTIFACT_VERSION}-${SHORT_COMMIT_HASH}"
              echo "Artifact version: $ARTIFACT_VERSION"
              echo "Artifact version: $ARTIFACT_LONG_VERSION"
              echo "##vso[task.setvariable variable=gitShortCommitHash;isOutput=true;isReadOnly=true]$SHORT_COMMIT_HASH"
              echo "##vso[task.setvariable variable=artifactVersion;isOutput=true;isReadOnly=true]$ARTIFACT_VERSION"
              echo "##vso[task.setvariable variable=artifactLongVersion;isOutput=true;isReadOnly=true]$ARTIFACT_LONG_VERSION"
              exit 0
            failOnStderr: true
            name: ExportArtifactVersion
            displayName: Export artifact version

          - task: JavaToolInstaller@1
            displayName: Set Java Version
            inputs:
              versionSpec: $(javaVersion)
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'

          - task: Maven@4
            displayName: Maven Build
            inputs:
              mavenVersionOption: $(mavenVersion)
              mavenOptions: "-Xmx4096m -Xms1024m"
              mavenPOMFile: '$(workingDirectoryJava)/pom.xml'
              goals: 'clean package'
              options: "
                --batch-mode --quiet -DskipTests -Dmaven.test.skip
                -Dsha1=-$(ExportArtifactVersion.gitShortCommitHash)"
              publishJUnitResults: true
              testResultsFiles: '$(workingDirectoryJava)/**/surefire-reports/TEST-*.xml'

          - task: Docker@2
            displayName: Build Docker image
            inputs:
              command: build
              repository: $(imageName)
              tags: |
                latest
                $(ExportArtifactVersion.artifactLongVersion)
              containerRegistry: $(serviceConnectionAcr)

          - task: Docker@2
            displayName: Push Docker Image
            inputs:
              command: 'push'
              repository: $(imageName)
              tags: |
                latest
                $(ExportArtifactVersion.artifactLongVersion)
              containerRegistry: $(serviceConnectionAcr)

          - bash: |
              set -e
              # Set git commit tags
              git tag $(ExportArtifactVersion.artifactLongVersion)
              git push origin $(ExportArtifactVersion.artifactLongVersion) --quiet
              echo "##vso[build.addbuildtag]$(ExportArtifactVersion.artifactLongVersion)"
            failOnStderr: true
            name: TagCommit
            displayName: Tag Git commit

  - stage: Deploy
    displayName: Deploy to Azure Web App
    variables:
      artifactLongVersion: $[ stageDependencies.Build.Build.outputs['ExportArtifactVersion.artifactLongVersion'] ]
    jobs:
      - job: DeployContainer
        displayName: Deploy to Azure
        steps:
          - task: AzureContainerApps@1
            displayName: Deploy to Container App
            inputs:
              azureSubscription: $(serviceConnection)
              containerAppName: $(containerAppName)
              resourceGroup: $(containerAppRgName)
              imageToDeploy: '$(containerRegistryUrl)/$(imageName):$(artifactLongVersion)'
