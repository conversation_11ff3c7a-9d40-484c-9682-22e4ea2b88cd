resource "azurerm_cognitive_account" "general" {
  name                  = "oai-${local.project_name}"
  resource_group_name   = module.resource_group_backend.name
  location              = module.resource_group_backend.location
  sku_name              = "S0"
  kind                  = "OpenAI"
  custom_subdomain_name = "gofore-${local.project_name}"

  tags = local.tags
}

resource "azurerm_cognitive_deployment" "general" {
  name                 = "oai-gpt-4o-mini-deployment"
  cognitive_account_id = azurerm_cognitive_account.general.id

  model {
    format  = "OpenAI"
    name    = "gpt-4o-mini"
    version = "2024-07-18"
  }

  sku {
    name     = "GlobalStandard"
    capacity = 800 # 800,000 tokens per minute
  }
}

resource "azurerm_cognitive_deployment" "gpt4_1_mini" {
  name                 = "oai-gpt-4.1-mini-deployment"
  cognitive_account_id = azurerm_cognitive_account.general.id

  model {
    format  = "OpenAI"
    name    = "gpt-4.1-mini"
    version = "2025-04-14"
  }

  sku {
    name     = "GlobalStandard"
    capacity = 1000
  }
}

resource "azurerm_cognitive_deployment" "gpt4_1" {
  name                 = "oai-gpt-4.1-deployment"
  cognitive_account_id = azurerm_cognitive_account.general.id

  model {
    format  = "OpenAI"
    name    = "gpt-4.1"
    version = "2025-04-14"
  }

  sku {
    name     = "GlobalStandard"
    capacity = 900
  }
}
