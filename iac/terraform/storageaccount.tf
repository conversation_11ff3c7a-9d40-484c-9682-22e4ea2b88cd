module "storage_account_function_app_tender" {
  source                   = "./modules/storageaccount"
  name                     = "${local.project_name}backend"
  resource_group_name      = module.resource_group_backend.name
  location                 = local.region_name_germany_west_central
  account_tier             = "Standard"
  account_replication_type = "LRS" # Could be optimized when going live
  tags                     = local.tags
}