locals {
  container_app_secret_entra_app_client_secret = "entra-app-client-secret"
  container_app_secret_cosmosdb_conn_string    = "cosmosdb-connection-string"
  cors_origin_portal                           = "https://${data.azurerm_key_vault_secret.swa_portal_hostname.value}"
  cors_origin_localhost                        = "http://localhost:4200"
  container_app_secret_ankoe_username = "ankoe-username"
  container_app_secret_ankoe_password = "ankoe-password"
}

data "azurerm_client_config" "current" {}

data "azurerm_key_vault_secret" "ankoe-username" {
  name         = "ankoe-username"
  key_vault_id = data.azurerm_key_vault.general.id
}

data "azurerm_key_vault_secret" "ankoe-password" {
  name         = "ankoe-password"
  key_vault_id = data.azurerm_key_vault.general.id
}

data "azurerm_log_analytics_workspace" "general" {
  name                = "la-aita-general"
  resource_group_name = data.azurerm_resource_group.general.name
}

resource "azurerm_container_app_environment" "backend" {
  name                       = "cae-${local.project_name}-backend"
  location                   = module.resource_group_backend.location
  resource_group_name        = module.resource_group_backend.name
  log_analytics_workspace_id = data.azurerm_log_analytics_workspace.general.id

  tags = local.tags
}

data "azurerm_user_assigned_identity" "backend" {
  name                = "id-aita-ca-backend"
  resource_group_name = data.azurerm_resource_group.general.name
}

resource "azurerm_role_assignment" "acr_pull_ca_backend" {
  principal_id                     = data.azurerm_user_assigned_identity.backend.principal_id
  role_definition_name             = "AcrPull"
  scope                            = azurerm_container_registry.acr.id
  skip_service_principal_aad_check = true
}

resource "azurerm_container_app" "backend" {
  depends_on = [azurerm_role_assignment.acr_pull_ca_backend]
  name                         = "ca-${local.project_name}-backend"
  container_app_environment_id = azurerm_container_app_environment.backend.id
  resource_group_name          = module.resource_group_backend.name
  revision_mode                = "Single"

  template {
    container {
      name   = "aita-backend"
      image  = "mcr.microsoft.com/k8se/quickstart:latest"
      cpu    = 0.5
      memory = "1Gi"

      env {
        name        = "SPRING_DATA_MONGODB_URI"
        secret_name = local.container_app_secret_cosmosdb_conn_string
      }

      env {
        name  = "APP_CORS_ORIGINS"
        value = "${local.cors_origin_portal},${local.cors_origin_localhost}"
      }

      env {
        name  = "APP_IDENTITY_MANAGEDIDENTITY_CLIENTID"
        value = data.azurerm_user_assigned_identity.backend.client_id
      }

      env {
        name  = "APP_OPENAI_ENDPOINT"
        value = azurerm_cognitive_account.general.endpoint
      }

      env {
        name  = "APP_OPENAI_DEPLOYMENTNAME"
        value = azurerm_cognitive_deployment.gpt4_1_mini.name
      }

      env {
        name        = "ANKOE_USERNAME"
        secret_name = local.container_app_secret_ankoe_username
      }

      env {
        name        = "ANKOE_PASSWORD"
        secret_name = local.container_app_secret_ankoe_password
      }


    }
    min_replicas = 1
    max_replicas = 1

  }

  registry {
    server   = azurerm_container_registry.acr.login_server
    identity = data.azurerm_user_assigned_identity.backend.id
  }

  ingress {
    external_enabled = true
    target_port      = 8080

    traffic_weight {
      percentage      = 100
      latest_revision = true
    }
  }

  identity {
    type = "UserAssigned"
    identity_ids = [data.azurerm_user_assigned_identity.backend.id]
  }

  secret {
    name                = local.container_app_secret_entra_app_client_secret
    identity            = data.azurerm_user_assigned_identity.backend.id
    key_vault_secret_id = data.azurerm_key_vault_secret.app_registration_client_secret.id
  }

  secret {
    name  = local.container_app_secret_cosmosdb_conn_string
    value = azurerm_cosmosdb_account.mongodb.primary_mongodb_connection_string
  }

  secret {
    name                = local.container_app_secret_ankoe_username
    identity            = data.azurerm_user_assigned_identity.backend.id
    key_vault_secret_id = data.azurerm_key_vault_secret.ankoe-username.id
  }

  secret {
    name                = local.container_app_secret_ankoe_password
    identity            = data.azurerm_user_assigned_identity.backend.id
    key_vault_secret_id = data.azurerm_key_vault_secret.ankoe-password.id
  }


  tags = local.tags

  lifecycle {
    ignore_changes = [template[0].container[0].image, template[0].http_scale_rule]
  }
}

# Workaround to configure the Entra authentication for the Container App since there is currently no direct support
# for this configuration via the Container App Resource above.
resource "azapi_resource" "cae_backend_authentication" {
  depends_on = [azurerm_container_app.backend]
  type      = "Microsoft.App/containerApps/authConfigs@2024-03-01"
  name      = "current"
  parent_id = azurerm_container_app.backend.id
  body = {
    properties = {
      encryptionSettings = {}
      globalValidation = {
        redirectToProvider          = "azureactivedirectory"
        unauthenticatedClientAction = "Return401"
      }
      identityProviders = {
        azureActiveDirectory = {
          registration = {
            clientId                = data.azurerm_key_vault_secret.app_registration_client_id.value
            clientSecretSettingName = local.container_app_secret_entra_app_client_secret
            openIdIssuer            = "https://sts.windows.net/${data.azurerm_client_config.current.tenant_id}/v2.0"
          }
          validation = {
            allowedAudiences = [
              "api://${data.azurerm_key_vault_secret.app_registration_client_id.value}"
            ]
            defaultAuthorizationPolicy = {
              allowedApplications = [
                data.azurerm_key_vault_secret.app_registration_client_id.value
              ]
            }
          }
        }
      }
      login = {
        cookieExpiration = {}
        nonce = {}
        preserveUrlFragmentsForLogins = false
        routes = {}
      }
      platform = {
        enabled = true
      }
    }
  }
}

# Workaround to configure the CORS settings for the Container App since there is currently no direct support
# for this configuration via the Container App Resource above.
# See: https://github.com/hashicorp/terraform-provider-azurerm/issues/21073
resource "azapi_resource_action" "cors" {
  depends_on = [azurerm_container_app.backend]
  type        = "Microsoft.App/containerApps@2023-05-01"
  resource_id = azurerm_container_app.backend.id
  method      = "PATCH"

  body = {
    properties = {
      configuration = {
        ingress = {
          corsPolicy = {
            allowedOrigins = [
              # Note: localhost is only for testing purposed - should be removed later on
              local.cors_origin_portal, local.cors_origin_portal
            ]
            allowedMethods = ["*"]
            allowedHeaders = ["*"]
          }
        }
      }
    }
  }
}

# Workaround to configure the cooldown settings for the Container App since there is currently no direct support
# for this configuration via the Container App Resource above.
# See: https://github.com/hashicorp/terraform-provider-azurerm/issues/28309
# More options: https://learn.microsoft.com/en-us/azure/templates/microsoft.app/containerapps?pivots=deployment-language-bicep
resource "azapi_resource_action" "cooldown_period" {
  depends_on = [azurerm_container_app.backend]
  type        = "Microsoft.App/containerApps@2024-10-02-preview"
  resource_id = azurerm_container_app.backend.id
  method      = "PATCH"

  body = {
    properties = {
      template = {
        scale = {
          cooldownPeriod = 60 * 60 # Set to 30 minutes
        }
      }
    }
  }
}
