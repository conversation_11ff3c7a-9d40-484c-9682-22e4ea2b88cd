data "azurerm_subscription" "current" {}

data "azurerm_user_assigned_identity" "policy_management" {
  name                = "id-aita-policy-management"
  resource_group_name = local.rg_name_general
}

data "azurerm_policy_definition_built_in" "require_resource_tag" {
  display_name = "Require a tag on resources"
}

resource "azurerm_subscription_policy_assignment" "require_resource_tag_business_unit" {
  name                 = "Require Resource tag - Business Unit"
  policy_definition_id = data.azurerm_policy_definition_built_in.require_resource_tag.id
  subscription_id      = data.azurerm_subscription.current.id
  enforce              = true
  parameters = jsonencode(
    {
      tagName = {
        value = "Business Unit"
      }
    }
  )

  non_compliance_message {
    content = "All Resources must have certain tags defined by the organization."
  }
}

resource "azurerm_subscription_policy_assignment" "require_resource_tag_project_name" {
  name                 = "Require Resource tag - Project Name"
  policy_definition_id = data.azurerm_policy_definition_built_in.require_resource_tag.id
  subscription_id      = data.azurerm_subscription.current.id
  enforce              = true
  parameters = jsonencode(
    {
      tagName = {
        value = "Project Name"
      }
    }
  )

  non_compliance_message {
    content = "All Resources must have certain tags defined by the organization."
  }
}

data "azurerm_policy_definition_built_in" "require_resource_group_tag" {
  display_name = "Require a tag on resource groups"
}

resource "azurerm_subscription_policy_assignment" "require_resource_group_tag_business_unit" {
  name                 = "Require Resource Group tag - Business Unit"
  policy_definition_id = data.azurerm_policy_definition_built_in.require_resource_group_tag.id
  subscription_id      = data.azurerm_subscription.current.id
  enforce              = true
  parameters = jsonencode(
    {
      tagName = {
        value = "Business Unit"
      }
    }
  )

  non_compliance_message {
    content = "All Resources must have certain tags defined by the organization."
  }
}

resource "azurerm_subscription_policy_assignment" "require_resource_group_tag_project_name" {
  name                 = "Require Resource Group tag - Project Name"
  policy_definition_id = data.azurerm_policy_definition_built_in.require_resource_group_tag.id
  subscription_id      = data.azurerm_subscription.current.id
  enforce              = true
  parameters = jsonencode(
    {
      tagName = {
        value = "Project Name"
      }
    }
  )

  non_compliance_message {
    content = "All Resources must have certain tags defined by the organization."
  }
}