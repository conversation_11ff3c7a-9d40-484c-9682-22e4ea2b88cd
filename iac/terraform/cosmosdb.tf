resource "azurerm_cosmosdb_account" "mongodb" {
  name                = "cosmon-${local.project_name}-backend"
  location            = module.resource_group_backend.location
  resource_group_name = module.resource_group_backend.name
  offer_type          = "Standard"
  kind                = "MongoDB"
  mongo_server_version = "7.0"

  automatic_failover_enabled = true

  capabilities {
    name = "EnableServerless"
  }

  capabilities {
    name = "EnableMongo"
  }

  # capabilities {
  #   name = "MongoDBv3.4"
  # }

  consistency_policy {
    consistency_level = "Session"
  }

  geo_location {
    location = local.region_name_germany_west_central # Primary location
    failover_priority = 0
  }

  tags = local.tags
}

resource "azurerm_cosmosdb_mongo_database" "tender" {
  name                = "${local.project_name}-tender"
  resource_group_name = azurerm_cosmosdb_account.mongodb.resource_group_name
  account_name        = azurerm_cosmosdb_account.mongodb.name
}

resource "azurerm_cosmosdb_mongo_collection" "tenders" {
  name                = "tenders"
  resource_group_name = azurerm_cosmosdb_mongo_database.tender.resource_group_name
  account_name        = azurerm_cosmosdb_account.mongodb.name
  database_name       = azurerm_cosmosdb_mongo_database.tender.name

  index {
    keys = ["_id"]
    unique = true
  }

  index {
    keys = ["creationTime"]
  }

  index {
    keys = ["lastUpdatedTime"]
  }

  index {
    keys = ["contractValue"]
  }
}

resource "azurerm_cosmosdb_mongo_collection" "extracted_documents" {
  name                = "extracted_documents"
  resource_group_name = azurerm_cosmosdb_mongo_database.tender.resource_group_name
  account_name        = azurerm_cosmosdb_account.mongodb.name
  database_name       = azurerm_cosmosdb_mongo_database.tender.name

  index {
    keys = ["_id"]
    unique = true
  }

  index {
    keys = ["tenderId"]
  }

  index {
    keys = ["fileId"]
    unique = true
  }
}

resource "azurerm_cosmosdb_mongo_collection" "config" {
  name                = "config"
  resource_group_name = azurerm_cosmosdb_mongo_database.tender.resource_group_name
  account_name        = azurerm_cosmosdb_account.mongodb.name
  database_name       = azurerm_cosmosdb_mongo_database.tender.name

  index {
    keys = ["_id"]
    unique = true
  }
}