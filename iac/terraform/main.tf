locals {
  project_name                     = "aita"
  rg_name_general                  = "rg-${local.project_name}-general"
  region_name_germany_west_central = "Germany West Central"
  tags = {
    "Business Unit" = "Gofore DACH"
    "Project Name"  = "AI Tender Analysis"
    "Managed by"    = "Terraform"
  }
  app_setting_entra_app_client_secret = "ENTRA_APP_CLIENT_SECRET"
}

module "resource_group_backend" {
  source   = "./modules/resourcegroup"
  name     = "${local.project_name}-backend"
  location = local.region_name_germany_west_central

  tags = local.tags
}

data "azurerm_resource_group" "general" {
  name = "rg-${local.project_name}-general"
}

data "azurerm_key_vault" "general" {
  name                = "kv-aita-general"
  resource_group_name = local.rg_name_general
}

data "azurerm_key_vault_secret" "app_registration_client_id" {
  name         = "app-registration-client-id"
  key_vault_id = data.azurerm_key_vault.general.id
}

data "azurerm_key_vault_secret" "app_registration_client_secret" {
  name         = "app-registration-client-secret-spa"
  key_vault_id = data.azurerm_key_vault.general.id
}

data "azurerm_key_vault_secret" "swa_portal_hostname" {
  name         = "static-web-app-portal-hostname"
  key_vault_id = data.azurerm_key_vault.general.id
}