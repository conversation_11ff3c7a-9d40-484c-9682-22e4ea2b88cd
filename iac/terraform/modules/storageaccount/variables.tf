variable "name" {
  description = "Name of the Storage Account"
  type        = string
}

variable "resource_group_name" {
  description = "Resource Group the Resource is assigned to"
  type        = string
}

variable "location" {
  description = "Location of the Resource"
  type        = string
}

variable "account_tier" {
  description = "Resource Group the Storage Account is assigned to"
  type        = string
}

variable "account_replication_type" {
  description = "Resource Group the Storage Account is assigned to"
  type        = string
}

variable "tags" {
  description = "Resource Tags"
  type = map(string)
}