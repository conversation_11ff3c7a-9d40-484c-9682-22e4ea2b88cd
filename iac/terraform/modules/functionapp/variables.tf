variable "name" {
  description = "Name of the Resource"
  type        = string
}

variable "location" {
  description = "Location of the Resource"
  type        = string
}

variable "resource_group_name" {
  description = "Resource Group the Resource is assigned to"
  type        = string
}

variable "tags" {
  description = "Resource Tags"
  type = map(string)
}

variable "auth_entra_app_client_id" {
  description = "Client ID of the Entra App which is responsible to authorize incoming client requests"
  type        = string
}

variable "cors_urls" {
  type = list(string)
  default = []
}

variable "app_runtime" {
  description = "The application runtime. Allowed values: Python, Java, Node"
  type        = string
}

variable "app_runtime_version" {
  description = "The runtime version of the configured `app_runtime`. Please see Azure docs for allowed values."
  default     = ""
  type        = string
}

variable "auth_entra_app_client_secret" {
  description = "Client secret of the Entra App which is responsible to authorize incoming client requests"
  type        = string
}

variable "app_settings" {
  description = "Custom app settings"
  default = {}
  type = map(string)
}