locals {
  function_name_short = replace("func${var.name}", "-", "")
  app_setting_entra_app_client_secret = "ENTRA_APP_CLIENT_SECRET"
}

data "azurerm_client_config" "current" {
}

module "storage_account_function_app_tender" {
  source                   = "../storageaccount"
  name                     = local.function_name_short
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
  tags                     = var.tags
}

resource "azurerm_service_plan" "this" {
  name                = "asp-${local.function_name_short}"
  resource_group_name = var.resource_group_name
  location            = var.location
  os_type             = "Linux"
  sku_name            = "Y1"
  tags                = var.tags
}

resource "azurerm_linux_function_app" "this" {
  name                = "func-${var.name}"
  resource_group_name = var.resource_group_name
  location            = var.location

  storage_account_name       = module.storage_account_function_app_tender.name
  storage_account_access_key = module.storage_account_function_app_tender.primary_access_key
  service_plan_id            = azurerm_service_plan.this.id

  app_settings = merge(
    tomap({
      (local.app_setting_entra_app_client_secret) = var.auth_entra_app_client_secret
    }), var.app_settings)


  site_config {
    dynamic "application_stack" {
      for_each = var.app_runtime == "Python" ? [0] : [
      ]
      content {
        python_version = var.app_runtime_version
      }
    }

    dynamic "application_stack" {
      for_each = var.app_runtime == "Node" ? [0] : [
      ]
      content {
        node_version = var.app_runtime_version
      }
    }

    dynamic "application_stack" {
      for_each = var.app_runtime == "Java" ? [0] : [
      ]
      content {
        java_version = var.app_runtime_version
      }
    }

    cors {
      allowed_origins = var.cors_urls
    }
  }

  auth_settings_v2 {
    auth_enabled           = true
    require_authentication = true
    unauthenticated_action = "Return401"
    login {}

    active_directory_v2 {
      client_id                  = var.auth_entra_app_client_id
      client_secret_setting_name = local.app_setting_entra_app_client_secret
      tenant_auth_endpoint       = "https://login.microsoftonline.com/${data.azurerm_client_config.current.tenant_id}/v2.0"
      allowed_applications = [var.auth_entra_app_client_id]
      allowed_audiences = ["api://${var.auth_entra_app_client_id}"]
    }
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      app_settings["WEBSITE_ENABLE_SYNC_UPDATE_SITE"],
      app_settings["WEBSITE_RUN_FROM_PACKAGE"]
    ]
  }
}
