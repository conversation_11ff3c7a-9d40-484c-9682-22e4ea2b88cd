# AiTenderAnalysis Backend

This repository contains the backend application of the system and also the Terraform configurations to manage Infrastructure as Code.

# Repository structure

The directory `app` contains the Spring Boot 3 backend application and all corresponding files. The directory `iac` contains the Terraform Infrastructure as Code configuration for the backend of the application.

# Getting Started

## Required software

* Maven 3.9.9
* Docker
* docker-compose
* Azure CLI (>= 2.70.0)
* Java 21 ([Eclipse Temurin Open JDK 21](https://adoptium.net/de/temurin/releases/))
  *Tip: [SDKMAN!](https://sdkman.io/) or [jEnv](https://github.com/jenv/jenv?tab=readme-ov-file) are recommended for switching Java versions on Mac/Linux.*
* Terraform (see `provider.tf` for required version)

## Build and Test

### Start application for local testing

To start the application on the local system, for example to have a test environment for the frontend application, you can make use of the *docker-compose* file which can be found in the `app` directory. This will start the backend application including a corresponding database in an isolated environment.

In case there is not yet a container image for the desired version of the backend application on your local system, you need to log in into the Azure Container Registry first, to allow Dock<PERSON> downloading the referenced image. You can log in by using the following Azure CLI command:

```commandline
az acr login -n <acr_name>
```

If successful, you can execute the following command to start up the local environment:

```commandline
docker-compose up
```

Alternatively, you can build an image on your local system:

```commandline
docker build -t <image_name> .
```

If not already done before, you have to compile and package the application before:

```commandline
mvn clean package -DskipTests
```

### Local development: Running with MongoDB and Debugging

For local development and debugging (e.g., with IntelliJ), you can:

* **Option 1 (recommended for most dev work):**
  Use Docker Compose to start only MongoDB locally. Then run the backend directly in your IDE (e.g., in debug mode, with the Spring profile set to `dev`).

    * *Hint:* The web section is commented out in `docker-compose.yaml` for this purpose.
    * In IntelliJ, set the active profile to `dev` in your Run/Debug Configuration.
    * You can connect to both the local (`port: 27017`) and the production MongoDB databases from within your IDE's database panel.

* **Option 2 (for working with production data):**
  Use your `application-dev.yml` to provide a direct connection string to the production database.

    * **Warning:** Only use production data for debugging if strictly necessary and with great care. Never check any production credentials or connection strings into version control.

*Remarks: To allow using Azure CLI tokens, you have to execute `az login` at least once.*

### Authentication against Azure Services

The application uses the `azure-identity` library to handle authentication when connecting to Azure services with the corresponding Azure SDKs. It allows creating the required `TokenCredential` object in different ways according to the execution environment. In the Cloud environment, authentication should be done via a User-assigned Managed Identity if supported. The identity has been created manually for the backend application. On the local system, tokens can be generated by using the Azure CLI, which means you're technically using your personal account to access the Azure Service. To support these and other options, the library provides different builder classes that can be used to create an instance of `TokenCredential`, for example, `ManagedIdentityCredentialBuilder` and `AzureCliCredentialBuilder`.

Please keep in mind that you have to assign the used identities the required role on the Azure service to enable access from the application. The roles can usually be found in the individual Azure Documentation of the service.

### Local configuration

For local development, please use the Spring Profile `dev` when starting the backend application as this will make use of the Azure CLI to acquire required access tokens for certain services (Note: This has been manually coded) and will also override default application properties by the properties defined in the `application-dev.yml` file. This way, you can also configure required connection strings and other parameters if you want to connect to Azure services from your local system (e.g., for testing or debugging).

*Remarks: To allow using Azure CLI tokens, you have to execute `az login` at least once.*

### OpenAPI Contract

The OpenAPI specification (`aita-api.yaml`) is maintained manually and should always be kept up-to-date when making changes to the backend API.
**Frontend development relies on this file** to generate TypeScript interfaces and API clients.
Whenever you update the backend API, remember to update the OpenAPI contract accordingly.

---

# Infrastructure as Code with Terraform

All changes to the Azure infrastructure should be made via the Terraform configurations in the `iac` directory.
**Important:**

* Do not make manual changes in the Azure Portal. The CI/CD pipeline will run Terraform and overwrite any out-of-band manual changes.
* Terraform is the **single source of truth** for all backend Azure infrastructure: databases, managed identities, networking, ACR, and more.

---

# CI/CD & Azure Integration

* The CI/CD pipeline in Azure DevOps will:

    1. Run Terraform to update all infrastructure as code.
    2. Build and push backend Docker images to Azure Container Registry (ACR).
    3. Deploy the backend and all related services to Azure.
* **Deployment to Azure will always use the latest IaC and image artifacts from the pipeline.**

---

# Troubleshooting

## Apple Silicon (M1/M2/M3) and Docker Images

If you are using a Mac with Apple Silicon (M\* chip) and encounter issues running the MongoDB image or the backend image, try the following workaround for local development:

* In your `docker-compose.yaml`, set the image for MongoDB to:



  ```yaml
  mongodb:
    image: mongo:7.0.15
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: aita-tender
  ```

## Database Tips

* Use the IntelliJ "Database" tool window to connect to either your local or production MongoDB instance for debugging or inspection (see screenshot).
* Be careful with production data: do **not** write test data or perform destructive operations when connected to prod.

## Secrets

* No secrets or credentials should ever be committed to the repository.
* All sensitive configuration (like connection strings) should be handled via Azure login and/or local `application-dev.yml`.
* If you add a MongoDB connection string or other secrets to `application-dev.yml` for local work, make sure **not** to check these in.

## Tests

* For information about unit and integration tests, see the README in the `test` directory.

---
