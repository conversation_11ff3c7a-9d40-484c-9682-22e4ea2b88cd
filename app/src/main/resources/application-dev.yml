spring:
  data:
    mongodb:
      uri: **************************************************************************************************************************************************************************************************************************************************************************
#      host: localhost:27017
#      username: admin
#      password: password
#      database: aita-tender
#      authentication-database: admin

app:
  cors:
    origins: http://localhost:4200
  openai:
    endpoint: https://gofore-aita.openai.azure.com/
    deployment-name: oai-gpt-4o-mini-deployment
  ankoe:
    username: ${ANKOE_USERNAME}
    password: ${ANKOE_PASSWORD}
  dtad:
    username: ${DTAD_USERNAME}
    password: ${DTAD_PASSWORD}

logging:
  level:
    com:
      gofore:
        aita: DEBUG
