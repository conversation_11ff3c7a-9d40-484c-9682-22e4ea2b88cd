spring:
  application:
    name: aita
  data:
    mongodb:
      database: aita-tender
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

management:
  health:
    mongo:
      enabled: false

app:
  cors:
    origins:
  storage:
    storage-account-name: staaitabackend
    document-container-name: documents
  identity:
    managed-identity:
      client-id:
  openai:
    endpoint:
      deployment-name:
  ankoe:
    username: ${ANKOE_USERNAME}
    password: ${ANKOE_PASSWORD}
  dtad:
    username: ${DTAD_USERNAME}
    password: ${DTAD_PASSWORD}
