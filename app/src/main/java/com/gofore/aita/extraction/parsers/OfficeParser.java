package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import java.io.IOException;
import java.io.InputStream;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.microsoft.OfficeParserConfig;
import org.apache.tika.parser.microsoft.ooxml.OOXMLParser;
import org.apache.tika.sax.BodyContentHandler;
import org.apache.tika.sax.ToXMLContentHandler;
import org.xml.sax.SAXException;

/** Parses .docx and .pptx (and other OOXML) */
public class OfficeParser implements IDocumentParser {

  private static final int DEFAULT_MAX_TEXT_LENGTH = 100 * 1024 * 1024;
  private final OfficeParserConfig config;

  public OfficeParser() {
    this.config = new OfficeParserConfig();

    config.setIncludeShapeBasedContent(true);
    config.setIncludeHeadersAndFooters(true);
    config.setIncludeDeletedContent(false);
    config.setExtractMacros(false);
    config.setIncludeMoveFromContent(false);
  }

  @Override
  public String parseToXhtml(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    OOXMLParser parser = new OOXMLParser();
    ParseContext ctx = new ParseContext();
    ctx.set(OfficeParserConfig.class, config);

    ToXMLContentHandler handler = new ToXMLContentHandler();
    parser.parse(in, handler, metadata, ctx);

    return handler.toString();
  }

  @Override
  public String parseToText(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    OOXMLParser parser = new OOXMLParser();
    BodyContentHandler handler = new BodyContentHandler(DEFAULT_MAX_TEXT_LENGTH);
    ParseContext context = new ParseContext();
    context.set(OfficeParserConfig.class, config);

    parser.parse(in, handler, metadata, context);
    return handler.toString();
  }
}
