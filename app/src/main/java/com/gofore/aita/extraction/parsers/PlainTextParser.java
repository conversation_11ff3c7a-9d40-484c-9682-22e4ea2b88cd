package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import org.apache.commons.io.IOUtils;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.xml.sax.SAXException;

public class PlainTextParser implements IDocumentParser {
  @Override
  public String parseToXhtml(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    // TODO: implement if needed
    return "";
  }

  @Override
  public String parseToText(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    return IOUtils.toString(in, StandardCharsets.UTF_8);
  }
}
