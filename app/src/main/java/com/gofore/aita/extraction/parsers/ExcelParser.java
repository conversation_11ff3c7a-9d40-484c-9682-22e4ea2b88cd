package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import java.io.IOException;
import java.io.InputStream;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.xml.sax.SAXException;

/** Parses .xls and .xlsx files */
public class ExcelParser implements IDocumentParser {

  @Override
  public String parseToXhtml(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {

    // auto-detects .xls vs .xlsx
    Workbook workbook = WorkbookFactory.create(in);

    DataFormatter formatter = new DataFormatter();
    StringBuilder html = new StringBuilder();

    // wrap in a container div
    html.append("<div class='excel-document'>\n");

    for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
      Sheet sheet = workbook.getSheetAt(i);
      String sheetName = sheet.getSheetName();

      // sheet heading
      html.append("<h2>").append(StringEscapeUtils.escapeHtml4(sheetName)).append("</h2>\n");
      html.append("<table>\n");

      for (Row row : sheet) {
        html.append("  <tr>\n");
        for (Cell cell : row) {
          // format the cell value as text
          String text = formatter.formatCellValue(cell);
          // escape HTML entities
          html.append("    <td>").append(StringEscapeUtils.escapeHtml4(text)).append("</td>\n");
        }
        html.append("  </tr>\n");
      }

      html.append("</table>\n");
    }

    html.append("</div>\n");
    workbook.close();

    return html.toString();
  }

  @Override
  public String parseToText(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    try (Workbook workbook = WorkbookFactory.create(in)) {
      DataFormatter formatter = new DataFormatter();
      StringBuilder text = new StringBuilder();

      for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
        Sheet sheet = workbook.getSheetAt(i);
        String sheetName = sheet.getSheetName();

        // add sheet name as header
        text.append("Blatt: ").append(sheetName).append("\n");
        text.append("=".repeat(sheetName.length() + 7)).append("\n\n");

        for (Row row : sheet) {
          boolean hasContent = false;
          StringBuilder rowText = new StringBuilder();

          for (Cell cell : row) {
            String cellValue = formatter.formatCellValue(cell);
            if (!cellValue.trim().isEmpty()) {
              hasContent = true;
              rowText.append(cellValue).append("\t");
            } else {
              rowText.append("\t");
            }
          }

          if (hasContent) {
            text.append(rowText.toString().trim()).append("\n");
          }
        }

        text.append("\n");
      }

      return text.toString();
    }
  }
}
