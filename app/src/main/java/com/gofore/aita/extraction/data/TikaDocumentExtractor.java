package com.gofore.aita.extraction.data;

import com.gofore.aita.extraction.api.IDocumentExtractor;
import com.gofore.aita.extraction.api.IDocumentParser;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.extraction.models.ExtractionStatus;
import com.gofore.aita.extraction.parsers.DocumentParserFactory;
import com.gofore.aita.extraction.utils.TokenCounter;
import com.gofore.aita.tender.data.IFileStore;
import com.gofore.aita.tender.domain.models.FileMetadata;
import java.io.InputStream;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.metadata.TikaCoreProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.xml.sax.SAXException;

@Component
public class TikaDocumentExtractor implements IDocumentExtractor {

  private static final Logger logger = LoggerFactory.getLogger(TikaDocumentExtractor.class);
  private final IFileStore fileStore;

  public TikaDocumentExtractor(IFileStore fileStore) {
    this.fileStore = fileStore;
  }

  @Override
  public ExtractedDocument extract(FileMetadata file) {
    if (file == null || file.getFileName() == null || file.getFilePath() == null) {
      logger.error("Invalid file metadata: {}", file);
      return createFailedDocument(file);
    }

    String fileName = file.getFileName();
    String ext = DocumentParserFactory.extensionFrom(fileName);

    try (InputStream in = fileStore.retrieveFile(file.getFilePath())) {
      if (in == null) {
        logger.error("Cannot find file at path {}", file.getFilePath());
        return createFailedDocument(file);
      }

      IDocumentParser parser = DocumentParserFactory.getParser(ext);
      return parseDocument(parser, in, file, ext);
    } catch (Exception e) {
      logger.error("Extraction failed for {}: {}", fileName, e.getMessage(), e);
      return createFailedDocument(file);
    }
  }

  private ExtractedDocument parseDocument(
      IDocumentParser parser, InputStream in, FileMetadata file, String extension) {
    try {
      Metadata metadata = new Metadata();
      metadata.set(TikaCoreProperties.RESOURCE_NAME_KEY, file.getFileName());

      String text = parser.parseToText(in, metadata);

      if (text == null || text.trim().isEmpty()) {
        logger.warn("No text extracted from file: {}", file.getFileName());
        return createFailedDocument(file);
      }

      int tokenCount = TokenCounter.countTokens(text);
      logger.info("Extracted {} tokens from file: {}", tokenCount, file.getFileName());

      return ExtractedDocument.builder()
          .fileId(file.getId())
          .fileName(file.getFileName())
          .text(text)
          .tokenCount(tokenCount)
          .status(ExtractionStatus.SUCCESS)
          .build();

    } catch (TikaException | SAXException te) {
      logger.error("Parsing error for file: {}", file.getFileName(), te);
      return createFailedDocument(file);
    } catch (Exception unexpected) {
      logger.error(
          "Unexpected error during extraction for file: {}", file.getFileName(), unexpected);
      return createFailedDocument(file);
    }
  }

  private ExtractedDocument createFailedDocument(FileMetadata file) {
    String fileId = file != null ? file.getId() : null;
    String fileName = file != null ? file.getFileName() : null;
    return ExtractedDocument.builder()
        .fileId(fileId)
        .fileName(fileName)
        .text("")
        .tokenCount(0)
        .status(ExtractionStatus.FAILED)
        .build();
  }
}
