package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import java.io.IOException;
import java.io.InputStream;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.pdf.PDFParser;
import org.apache.tika.parser.pdf.PDFParserConfig;
import org.apache.tika.sax.BodyContentHandler;
import org.apache.tika.sax.ToXMLContentHandler;
import org.xml.sax.SAXException;

public class PdfParser implements IDocumentParser {

  private static final int DEFAULT_MAX_TEXT_LENGTH = 100 * 1024 * 1024;
  private final PDFParserConfig config;

  public PdfParser() {
    this.config = new PDFParserConfig();
    config.setExtractInlineImages(false);
    config.setSortByPosition(true);
    config.setSuppressDuplicateOverlappingText(true);
    config.setDetectAngles(true);
    config.setExtractAnnotationText(true);
    config.setExtractAcroFormContent(true);
  }

  @Override
  public String parseToXhtml(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {

    PDFParser parser = new PDFParser();
    ParseContext ctx = new ParseContext();
    ctx.set(PDFParserConfig.class, config);

    ToXMLContentHandler handler = new ToXMLContentHandler();
    parser.parse(in, handler, metadata, ctx);

    return handler.toString();
  }

  @Override
  public String parseToText(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    PDFParser parser = new PDFParser();
    BodyContentHandler handler = new BodyContentHandler(DEFAULT_MAX_TEXT_LENGTH);
    ParseContext context = new ParseContext();
    context.set(PDFParserConfig.class, config);

    parser.parse(in, handler, metadata, context);
    return handler.toString();
  }
}
