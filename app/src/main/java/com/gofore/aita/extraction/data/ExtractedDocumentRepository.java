package com.gofore.aita.extraction.data;

import com.gofore.aita.extraction.models.ExtractedDocument;
import java.util.List;
import java.util.Optional;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ExtractedDocumentRepository extends MongoRepository<ExtractedDocument, String> {

  Optional<ExtractedDocument> findByFileId(String fileId);

  List<ExtractedDocument> findByTenderId(String tenderId);

  void deleteByTenderId(String tenderId);
}
