package com.gofore.aita.extraction.api;

import java.io.IOException;
import java.io.InputStream;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.xml.sax.SAXException;

public interface IDocumentParser {
  /**
   * Parse the incoming stream to XHTML (with page markers). Returns an XHTML string that preserves
   * headings, paragraphs, tables, and per-page divisions.
   */
  String parseToXhtml(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException;

  String parseToText(InputStream in, Metadata metadata)
      throws <PERSON>OException, TikaException, SAXException;
}
