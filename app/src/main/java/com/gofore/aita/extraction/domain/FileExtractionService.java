package com.gofore.aita.extraction.domain;

import com.gofore.aita.extraction.data.ExtractedDocumentRepository;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.extraction.models.ExtractionStatus;
import com.gofore.aita.tender.domain.models.FileMetadata;
import com.gofore.aita.tender.domain.models.Tender;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class FileExtractionService {

  private final ExtractionService extractionService;
  private final ExtractedDocumentRepository extractedDocumentRepository;

  private static final Set<String> SUPPORTED_EXTENSIONS =
      Set.of("doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt", "csv");

  // keywords that flag a file as a "form" (we skip these for AI analysis)
  private static final Set<String> FORM_KEYWORDS =
      Set.of("formblatt", "formbl", "formblaetter", "formbl-tter", "formblätter", "vorlage");

  // "preisblatt",
  // "statistische information",
  // "erklärung",
  // "erkl");

  /**
   * Processes files for extraction, either retrieving existing extracted text or extracting text
   * from files.
   *
   * @param tender The tender containing files to process
   * @return List of extracted documents with text and token counts
   */
  public List<ExtractedDocument> processFilesForExtraction(Tender tender) {
    List<ExtractedDocument> extractedDocuments = new ArrayList<>();
    List<FileMetadata> files = tender.getFiles();

    if (CollectionUtils.isEmpty(files)) {
      log.info("No files to process for tender: {}", tender.getId());
      return extractedDocuments;
    }

    log.info("Processing {} files for extraction for tender: {}", files.size(), tender.getId());

    for (FileMetadata file : files) {
      String fileName = file.getFileName();
      String extension = FilenameUtils.getExtension(fileName).toLowerCase();

      if (!SUPPORTED_EXTENSIONS.contains(extension)) {
        log.info("Skipping unsupported file type '{}'.", fileName);
        continue;
      }

      // forms are not needed for analysis
      boolean isForm = isFormFile(fileName);
      if (isForm) {
        log.info("Skipping form file '{}'.", fileName);
        continue;
      }

      try {
        Optional<ExtractedDocument> existingExtraction =
            extractedDocumentRepository.findByFileId(file.getId());

        if (existingExtraction.isPresent()
            && existingExtraction.get().getStatus() == ExtractionStatus.SUCCESS) {
          ExtractedDocument doc = existingExtraction.get();

          // legacy --> some extractions have no file name yet
          if (doc.getFileName() == null) {
            existingExtraction.get().setFileName(fileName);
            extractedDocumentRepository.save(existingExtraction.get());
            doc.setFileName(fileName);
          }

          log.info(
              "Using existing extraction for file: {} (tokens: {})",
              file.getFileName(),
              existingExtraction.get().getTokenCount());
          doc.setFileExtension(extension);
          doc.setIsForm(isForm);
          extractedDocuments.add(existingExtraction.get());
        } else {
          // fresh extraction
          log.info("Extracting text from file: {}", file.getFileName());
          ExtractedDocument extractedDocument =
              extractionService.startExtraction(tender.getId(), file);

          extractedDocument.setFileExtension(extension);
          extractedDocument.setIsForm(isForm);
          extractedDocument = extractedDocumentRepository.save(extractedDocument);

          if (extractedDocument.getStatus() == ExtractionStatus.SUCCESS) {
            extractedDocuments.add(extractedDocument);
            log.info(
                "Successfully extracted text from file: {} (tokens: {})",
                file.getFileName(),
                extractedDocument.getTokenCount());
          } else {
            log.warn("Extraction failed for file: {}", file.getFileName());
          }
        }
      } catch (Exception e) {
        log.error(
            "Error processing file for extraction: {} (tenderId: {})",
            file.getFileName(),
            tender.getId(),
            e);
      }
    }

    log.info(
        "Processed {} files, extracted text from {} files",
        files.size(),
        extractedDocuments.size());

    return extractedDocuments;
  }

  private boolean isFormFile(String fileName) {
    String lower = fileName.toLowerCase();
    return FORM_KEYWORDS.stream().anyMatch(lower::contains);
  }
}
