package com.gofore.aita.extraction.domain;

import com.gofore.aita.extraction.api.IDocumentExtractor;
import com.gofore.aita.extraction.data.ExtractedDocumentRepository;
import com.gofore.aita.extraction.exceptions.ExtractionException;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.tender.domain.models.FileMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ExtractionService {

  private static final Logger logger = LoggerFactory.getLogger(ExtractionService.class);

  private final IDocumentExtractor documentExtractor;
  private final ExtractedDocumentRepository extractedDocumentRepository;

  public ExtractionService(
      IDocumentExtractor documentExtractor,
      ExtractedDocumentRepository extractedDocumentRepository) {
    this.documentExtractor = documentExtractor;
    this.extractedDocumentRepository = extractedDocumentRepository;
  }

  public ExtractedDocument startExtraction(String tenderId, FileMetadata file) {
    logger.info("Starting extraction for file: {} (tenderId: {})", file.getFileName(), tenderId);

    try {
      ExtractedDocument extractedDocument = documentExtractor.extract(file);
      extractedDocument.setTenderId(tenderId);
      ExtractedDocument savedDocument = extractedDocumentRepository.save(extractedDocument);

      logger.info(
          "Extraction completed for file: {} (tenderId: {}, status: {})",
          file.getFileName(),
          tenderId,
          savedDocument.getStatus());

      return savedDocument;
    } catch (ExtractionException e) {
      logger.error(
          "Extraction failed for file: {} (tenderId: {})", file.getFileName(), tenderId, e);
      throw e;
    }
  }
}
