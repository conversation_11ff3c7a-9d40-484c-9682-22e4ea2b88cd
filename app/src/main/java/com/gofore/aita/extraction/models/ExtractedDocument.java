package com.gofore.aita.extraction.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "extracted_documents")
public class ExtractedDocument {
  @Id private String id;

  private String tenderId;

  private String fileId;
  private String fileName;
  private String fileExtension;

  private String text;
  private int tokenCount;

  private ExtractionStatus status;

  private Boolean isForm;
}
