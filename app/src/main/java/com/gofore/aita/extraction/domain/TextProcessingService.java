package com.gofore.aita.extraction.domain;

import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.extraction.utils.TokenCounter;
import java.util.List;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class TextProcessingService {

  /**
   * Combines tender description with extracted documents in a structured format. Now iterates each
   * SectionChunk and inlines its title + markdown.
   */
  public TextWithTokenCount combineTexts(
      String description, List<ExtractedDocument> extractedDocuments) {
    StringBuilder combined = new StringBuilder();
    int totalTokens = 0;

    // tender description
    if (StringUtils.hasText(description)) {
      combined.append("AUSSCHREIBUNGSBESCHREIBUNG:\n").append(description).append("\n\n");
      totalTokens += TokenCounter.countTokens(description);
    }

    // each documents extracted text
    if (!CollectionUtils.isEmpty(extractedDocuments)) {
      combined.append("EXTRAHIERTER TEXT DER AUSSCHREIBUNGSDOKUMENTE:\n\n");

      for (int i = 0; i < extractedDocuments.size(); i++) {
        ExtractedDocument doc = extractedDocuments.get(i);
        if (Boolean.TRUE.equals(doc.getIsForm())) {
          continue;
        }

        combined
            .append("--- DOKUMENT ")
            .append(i + 1)
            .append(": ")
            .append(doc.getFileName())
            .append(" ---\n\n");

        combined.append(doc.getText()).append("\n\n");
        totalTokens += TokenCounter.countTokens(doc.getText());
      }
    }

    // prompts/formatting
    totalTokens += 100;

    return new TextWithTokenCount(combined.toString(), totalTokens);
  }

  @Value
  public static class TextWithTokenCount {
    String text;
    int tokenCount;
  }
}
