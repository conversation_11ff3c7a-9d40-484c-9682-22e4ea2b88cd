package com.gofore.aita.extraction.parsers;

import com.gofore.aita.extraction.api.IDocumentParser;
import java.io.IOException;
import java.io.InputStream;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.sax.BodyContentHandler;
import org.apache.tika.sax.ToXMLContentHandler;
import org.xml.sax.SAXException;

/** Fallback parser */
public class AutoDetectDefaultParser implements IDocumentParser {

  private static final int DEFAULT_MAX_TEXT_LENGTH = 100 * 1024 * 1024;
  private final AutoDetectParser parser;

  public AutoDetectDefaultParser() {
    this.parser = new AutoDetectParser();
  }

  @Override
  public String parseToXhtml(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    ToXMLContentHandler handler = new ToXMLContentHandler();
    ParseContext ctx = new ParseContext();
    parser.parse(in, handler, metadata, ctx);
    return handler.toString();
  }

  @Override
  public String parseToText(InputStream in, Metadata metadata)
      throws IOException, TikaException, SAXException {
    BodyContentHandler handler = new BodyContentHandler(DEFAULT_MAX_TEXT_LENGTH);
    ParseContext context = new ParseContext();

    parser.parse(in, handler, metadata, context);
    return handler.toString();
  }
}
