package com.gofore.aita.tender.api.dto;

import lombok.Getter;

@Getter
public enum CollectionSortDirectionDTO {
  ASC("asc"),
  DESC("desc");

  private final String fieldName;

  CollectionSortDirectionDTO(String fieldName) {
    this.fieldName = fieldName;
  }

  public static CollectionSortDirectionDTO fromValue(String value) {
    for (CollectionSortDirectionDTO field : values()) {
      if (field.fieldName.equalsIgnoreCase(value)) {
        return field;
      }
    }
    throw new IllegalArgumentException("Unsupported SortDirection: " + value);
  }
}
