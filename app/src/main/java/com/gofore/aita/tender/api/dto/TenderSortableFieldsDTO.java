package com.gofore.aita.tender.api.dto;

import lombok.Getter;

@Getter
public enum TenderSortableFieldsDTO {
  CONTRACT_VALUE("contractValue"),
  CREATION_TIME("creationTime"),
  LAST_UPDATED_TIME("lastUpdatedTime");

  private final String fieldName;

  TenderSortableFieldsDTO(String fieldName) {
    this.fieldName = fieldName;
  }

  public static TenderSortableFieldsDTO fromValue(String value) {
    for (TenderSortableFieldsDTO field : values()) {
      if (field.fieldName.equalsIgnoreCase(value)) {
        return field;
      }
    }
    throw new IllegalArgumentException("Unsupported TenderSortField: " + value);
  }
}
