package com.gofore.aita.tender.api;

import com.gofore.aita.tender.api.dto.*;
import com.gofore.aita.tender.api.mapper.AnalysisResultMapper;
import com.gofore.aita.tender.api.mapper.FileMetadataMapper;
import com.gofore.aita.tender.api.mapper.TenderCreateSimpleMapper;
import com.gofore.aita.tender.api.mapper.TenderMapper;
import com.gofore.aita.tender.api.mapper.TenderUpdateMapper;
import com.gofore.aita.tender.api.util.CredentialHelper;
import com.gofore.aita.tender.domain.TenderService;
import com.gofore.aita.tender.domain.exceptions.AITenderCreationExtractionException;
import com.gofore.aita.tender.domain.exceptions.BadRequestException;
import com.gofore.aita.tender.domain.mapper.UserMapper;
import com.gofore.aita.tender.domain.models.FileDownloadRequest;
import com.gofore.aita.tender.domain.models.FileMetadata;
import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.TenderUpdate;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping("/tenders")
public class TenderController {

  private final TenderMapper tenderMapper;
  private final TenderUpdateMapper tenderUpdateMapper;
  private final TenderCreateSimpleMapper tenderCreateSimpleMapper;
  private final TenderService tenderService;
  private final UserMapper userMapper;
  private final FileMetadataMapper fileMetadataMapper;
  private final AnalysisResultMapper analysisResultMapper;

  public TenderController(
      TenderMapper tenderMapper,
      TenderUpdateMapper tenderUpdateMapper,
      TenderCreateSimpleMapper tenderCreateSimpleMapper,
      TenderService tenderService,
      UserMapper userMapper,
      FileMetadataMapper fileMetadataMapper,
      AnalysisResultMapper analysisResultMapper) {
    this.tenderMapper = tenderMapper;
    this.tenderUpdateMapper = tenderUpdateMapper;
    this.tenderCreateSimpleMapper = tenderCreateSimpleMapper;
    this.tenderService = tenderService;
    this.userMapper = userMapper;
    this.fileMetadataMapper = fileMetadataMapper;
    this.analysisResultMapper = analysisResultMapper;
  }

  @GetMapping()
  public ResponseEntity<TenderPagedDTO> getAllTenders(
      @RequestParam int page,
      @RequestParam int size,
      @RequestParam(required = false) TenderSortableFieldsDTO sortBy,
      @RequestParam(required = false) CollectionSortDirectionDTO sortDirection) {
    Pageable pageable =
        PageRequest.of(
            page,
            size,
            (sortBy == null)
                ? Sort.unsorted()
                : Sort.by(Sort.Direction.valueOf(sortDirection.name()), sortBy.getFieldName()));
    Page<Tender> pageTender = tenderService.getAll(pageable);
    List<TenderDTO> tenderList = pageTender.stream().map(tenderMapper::toTenderDTO).toList();
    PagingDTO pagingDTO =
        new PagingDTO(
            pageTender.getTotalElements(),
            pageTender.getTotalPages(),
            pageTender.getNumber(),
            pageTender.getSize());
    TenderPagedDTO response = new TenderPagedDTO(tenderList, pagingDTO);
    return ResponseEntity.ok(response);
  }

  @GetMapping("{id}")
  public ResponseEntity<TenderDTO> getTenderById(@PathVariable String id) {
    Optional<Tender> tender = tenderService.get(id);
    return tender
        .map(value -> new ResponseEntity<>(tenderMapper.toTenderDTO(value), HttpStatus.OK))
        .orElseGet(() -> new ResponseEntity<>(HttpStatus.NOT_FOUND));
  }

  @PostMapping(consumes = {"multipart/form-data"})
  public ResponseEntity<TenderDTO> createTender(
      @ModelAttribute @Valid TenderCreateDTO tenderCreateDTO, @RequestHeader HttpHeaders headers) {
    Optional.ofNullable(tenderCreateDTO.getFiles())
        .ifPresent(
            multipartFiles ->
                multipartFiles.forEach(
                    file -> {
                      if (file.isEmpty())
                        throw new BadRequestException(
                            "An empty file has been provided [filename: %s]"
                                .formatted(file.getOriginalFilename()));
                    }));
    Tender tender = tenderMapper.toTender(tenderCreateDTO);
    tender =
        tenderService.save(
            tender,
            tenderCreateDTO.getFiles(),
            userMapper.toUser(CredentialHelper.extractUserInformation(headers)));
    TenderDTO response = tenderMapper.toTenderDTO(tender);
    return new ResponseEntity<>(response, HttpStatus.CREATED);
  }

  @PostMapping(
      path = "create-with-ai",
      consumes = {"multipart/form-data"})
  public ResponseEntity<TenderDTO> createTenderWithAI(
      @ModelAttribute @Valid TenderCreateSimpleDTO tenderCreateSimpleDTO,
      @RequestHeader HttpHeaders headers) {
    Optional.ofNullable(tenderCreateSimpleDTO.getFiles())
        .ifPresent(
            multipartFiles ->
                multipartFiles.forEach(
                    file -> {
                      if (file.isEmpty())
                        throw new BadRequestException(
                            "An empty file has been provided [filename: %s]"
                                .formatted(file.getOriginalFilename()));
                    }));
    Tender tender = tenderCreateSimpleMapper.toTender(tenderCreateSimpleDTO);
    tender =
        tenderService.createTenderWithAI(
            tender,
            tenderCreateSimpleDTO.getFiles(),
            userMapper.toUser(CredentialHelper.extractUserInformation(headers)));
    if (tender == null) {
      throw new AITenderCreationExtractionException(
          "AI‐assisted tender creation failed, fields could not be extracted.");
    }
    TenderDTO response = tenderMapper.toTenderDTO(tender);
    return new ResponseEntity<>(response, HttpStatus.CREATED);
  }

  @PutMapping("{id}")
  public ResponseEntity<TenderDTO> updateTender(
      @PathVariable String id,
      @RequestBody TenderUpdateDTO tenderUpdateDTO,
      @RequestHeader HttpHeaders headers) {
    TenderUpdate tender = tenderUpdateMapper.toTenderUpdate(tenderUpdateDTO);
    Tender updatedTender =
        tenderService.update(
            id, tender, userMapper.toUser(CredentialHelper.extractUserInformation(headers)));
    return new ResponseEntity<>(tenderMapper.toTenderDTO(updatedTender), HttpStatus.OK);
  }

  @DeleteMapping("{id}")
  public ResponseEntity<Void> deleteTender(@PathVariable String id) {
    tenderService.delete(id);
    return new ResponseEntity<>(HttpStatus.NO_CONTENT);
  }

  @DeleteMapping("{tenderId}/files/{fileId}")
  public ResponseEntity<Void> deleteTenderFile(
      @PathVariable String tenderId,
      @PathVariable String fileId,
      @RequestHeader HttpHeaders headers) {
    tenderService.deleteFile(
        tenderId, fileId, userMapper.toUser(CredentialHelper.extractUserInformation(headers)));
    return new ResponseEntity<>(HttpStatus.NO_CONTENT);
  }

  @PostMapping(
      path = "{tenderId}/files",
      consumes = {"multipart/form-data"})
  public ResponseEntity<FileMetadataDTO> addTenderFile(
      @PathVariable String tenderId,
      @RequestParam("file") MultipartFile[] files,
      @RequestHeader HttpHeaders headers) {
    Arrays.stream(files)
        .forEach(
            file -> {
              if (file.isEmpty()) {
                throw new BadRequestException("File is empty");
              }
            });
    if (files.length > 1) {
      throw new BadRequestException("More than one file has been provided");
    }
    FileMetadata fileMetadata =
        tenderService.addFile(
            tenderId,
            files[0],
            userMapper.toUser(CredentialHelper.extractUserInformation(headers)));
    return new ResponseEntity<>(fileMetadataMapper.map(fileMetadata), HttpStatus.CREATED);
  }

  @GetMapping(
      value = "{tenderId}/files/{fileId}",
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public ResponseEntity<InputStreamResource> downloadTenderFile(
      @PathVariable String tenderId, @PathVariable String fileId) {
    log.debug("Downloading file {} for tender {}", fileId, tenderId);
    FileDownloadRequest downloadRequest = tenderService.downloadFile(tenderId, fileId);
    // File will be streamed to the client
    return tenderService.buildFileDownloadResponse(downloadRequest);
  }

  @PostMapping(path = "{tenderId}/analyze")
  public ResponseEntity<AnalysisResultDTO> analyzeTender(@PathVariable String tenderId) {
    AnalysisResultDTO analysisResult =
        analysisResultMapper.map(tenderService.startAnalysis(tenderId));
    return ResponseEntity.ok(analysisResult);
  }
}
