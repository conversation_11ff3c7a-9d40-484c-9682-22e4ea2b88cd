package com.gofore.aita.tender.domain.models;

import lombok.Data;

@Data
public class TenderUpdate {
  private String title;
  private String sourceUrl;
  private String client;
  private String submissionDate;
  private String bindingDeadline;
  private String contractDuration;
  private String publicationDate;
  private String questionDeadline;
  private Float contractValue;
  private String maximumBudget;
  private String winningCriteria;
  private String weightingPriceQuality;
  private String deliveryLocation;
  private String description;
  private WorkflowStatus workflowStatus;

  // User evaluation fields
  private String comment;
  private Integer rating;
  private Boolean isFavorite;
}
