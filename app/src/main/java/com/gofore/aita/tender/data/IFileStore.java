package com.gofore.aita.tender.data;

import java.io.InputStream;
import org.springframework.data.util.Pair;

/** An interface for a file store. */
public interface IFileStore {

  /**
   * Store file in a store.
   *
   * @param folderName Name of the folder where file should be stored
   * @param fileName Name of the file
   * @param fileStream Stream representing the file
   * @param fileSizeBytes File size in Bytes
   * @return File path within that store
   */
  Pair<String, String> storeFile(
      String folderName, String fileName, InputStream fileStream, long fileSizeBytes);

  /**
   * Delete file from store
   *
   * @param filePath Full path to file
   */
  void deleteFile(String filePath);

  /**
   * Retrieve file from store.
   *
   * @param filePath Path to file
   * @return InputStream that allows streaming the file
   */
  InputStream retrieveFile(String filePath);
}
