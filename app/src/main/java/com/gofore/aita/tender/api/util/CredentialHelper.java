package com.gofore.aita.tender.api.util;

import com.gofore.aita.tender.domain.models.ClientCredentials;
import org.springframework.http.HttpHeaders;

public class CredentialHelper {

  public static final String AUTH_HEADER_CLIENT_PRINCIPAL_ID = "X-MS-CLIENT-PRINCIPAL-ID";
  public static final String AUTH_HEADER_CLIENT_PRINCIPAL_NAME = "X-MS-CLIENT-PRINCIPAL-NAME";
  public static final String AUTH_HEADER_CLIENT_PRINCIPAL = "X-MS-CLIENT-PRINCIPAL";

  public static ClientCredentials extractUserInformation(HttpHeaders headers) {
    String principalId = headers.getFirst(AUTH_HEADER_CLIENT_PRINCIPAL_ID);
    String principalName = headers.getFirst(AUTH_HEADER_CLIENT_PRINCIPAL_NAME);
    String principal = headers.getFirst(AUTH_HEADER_CLIENT_PRINCIPAL);
    return new ClientCredentials(principalId, principalName, principal);
  }
}
