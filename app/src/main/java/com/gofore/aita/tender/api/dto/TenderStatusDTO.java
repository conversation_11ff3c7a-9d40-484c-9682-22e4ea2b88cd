package com.gofore.aita.tender.api.dto;

import lombok.Getter;

@Getter
public enum TenderStatusDTO {
  NEW("New"),
  ANALYZING("Analyzing"),
  ANALYZED("Analyzed"),
  BID_ANALYZED("Bid-Analyzed"),
  CLOSED("Closed");

  private final String displayName;

  TenderStatusDTO(String displayName) {
    this.displayName = displayName;
  }

  public static TenderStatusDTO fromValue(String value) {
    for (TenderStatusDTO status : values()) {
      if (status.displayName.equalsIgnoreCase(value)) {
        return status;
      }
    }
    throw new IllegalArgumentException("Unsupported TenderStatus: " + value);
  }
}
