package com.gofore.aita.tender.api.dto;

import jakarta.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class TenderCreateSimpleDTO {

  @NotBlank private String title;

  private String sourceUrl;

  @NotBlank private String description;

  List<MultipartFile> files = new ArrayList<>();
}
