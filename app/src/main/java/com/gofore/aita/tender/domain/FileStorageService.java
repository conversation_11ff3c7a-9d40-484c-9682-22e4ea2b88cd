package com.gofore.aita.tender.domain;

import com.gofore.aita.tender.data.IFileStore;
import com.gofore.aita.tender.domain.models.FileMetadata;
import java.io.InputStream;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

@Configuration
@Service
@Slf4j
public class FileStorageService {

  private final IFileStore fileStore;

  public FileStorageService(IFileStore fileStore) {
    this.fileStore = fileStore;
  }

  /**
   * Store file and return metadata representation.
   *
   * @param folderName Name of the folder where file should be stored
   * @param fileName Name of the file
   * @param fileStream Stream representing the file
   * @param fileSizeBytes File size in Bytes
   * @return Metadata representation for uploaded file
   */
  public FileMetadata storeFile(
      String folderName, String fileName, InputStream fileStream, long fileSizeBytes) {
    Pair<String, String> storeResult =
        fileStore.storeFile(folderName, fileName, fileStream, fileSizeBytes);
    log.debug(
        "Stored file, returned slugifiedName: {} and filePath: {}",
        storeResult.getFirst(),
        storeResult.getSecond());
    FileMetadata fileMetadata =
        new FileMetadata(
            String.valueOf(UUID.randomUUID()),
            fileName,
            storeResult.getFirst(),
            storeResult.getSecond(),
            fileSizeBytes);
    log.debug("build metadata: {}", fileMetadata);
    return fileMetadata;
  }

  /**
   * Delete file from storage.
   *
   * @param file Metadata representation for file to be deleted
   */
  public void delete(FileMetadata file) {
    fileStore.deleteFile(file.getFilePath());
  }

  public InputStream getFile(FileMetadata existingFile) {
    return fileStore.retrieveFile(existingFile.getFilePath());
  }
}
