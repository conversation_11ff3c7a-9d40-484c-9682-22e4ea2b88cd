package com.gofore.aita.tender.domain.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public enum WorkflowStatus {
  @JsonProperty("New")
  NEW("New"),
  @<PERSON>sonProperty("In-Work")
  IN_WORK("In-Work"),
  @JsonProperty("Declined")
  DECLINED("Declined"),
  @JsonProperty("Offered")
  OFFERED("Offered"),
  @JsonProperty("Done")
  DONE("Done");

  private final String displayName;

  WorkflowStatus(String displayName) {
    this.displayName = displayName;
  }

  public static WorkflowStatus fromValue(String value) {
    for (WorkflowStatus status : values()) {
      if (status.displayName.equalsIgnoreCase(value)) {
        return status;
      }
    }
    throw new IllegalArgumentException("Unsupported WorkflowStatus: " + value);
  }
}
