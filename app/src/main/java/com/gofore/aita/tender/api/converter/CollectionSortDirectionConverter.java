package com.gofore.aita.tender.api.converter;

import com.gofore.aita.tender.api.dto.CollectionSortDirectionDTO;
import org.springframework.core.convert.converter.Converter;

public class CollectionSortDirectionConverter
    implements Converter<String, CollectionSortDirectionDTO> {

  @Override
  public CollectionSortDirectionDTO convert(String source) {
    return CollectionSortDirectionDTO.fromValue(source);
  }
}
