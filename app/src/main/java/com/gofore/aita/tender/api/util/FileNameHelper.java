package com.gofore.aita.tender.api.util;

import com.github.slugify.Slugify;
import java.nio.charset.StandardCharsets;
import java.text.Normalizer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileNameHelper {

  private static final Slugify slugify = Slugify.builder().build();
  private static final String DEFAULT_FILENAME = "download";

  /**
   * Processes the file name by fixing encoding issues and normalizing it. Main entry point for
   * filename processing.
   */
  public static String processFileName(String fileName) {
    if (isBlank(fileName)) {
      log.warn("Empty or null filename provided, using default");
      return DEFAULT_FILENAME;
    }

    try {
      return fixUtf8FilenameBug(fileName);
    } catch (Exception e) {
      log.warn("Failed to process filename '{}', using default: {}", fileName, e.getMessage());
      return DEFAULT_FILENAME;
    }
  }

  /** Creates an ASCII-safe version of the filename suitable for older browsers. */
  public static String getAsciiSafeName(String fileName) {
    if (isBlank(fileName)) {
      return DEFAULT_FILENAME;
    }

    try {
      return getSlugifiedName(fileName);
    } catch (Exception e) {
      log.warn("Failed to create ASCII-safe name for '{}': {}", fileName, e.getMessage());
      return DEFAULT_FILENAME;
    }
  }

  /** Normalizes and slugifies the provided file name. Preserves the original file extension. */
  public static String getSlugifiedName(String fileName) {
    if (isBlank(fileName)) {
      return DEFAULT_FILENAME;
    }

    String normalized = Normalizer.normalize(fileName, Normalizer.Form.NFC);
    int dotIndex = normalized.lastIndexOf('.');

    if (dotIndex <= 0 || dotIndex == normalized.length() - 1) {
      // no extension
      return slugify.slugify(normalized);
    }

    String baseName = normalized.substring(0, dotIndex);
    String extension = normalized.substring(dotIndex);
    return slugify.slugify(baseName) + extension;
  }

  /**
   * Temporary fix for UTF-8 encoding issues in filenames. TODO: Remove this once the root cause is
   * addressed in the file storage system.
   */
  public static String fixUtf8FilenameBug(String filename) {
    if (isBlank(filename)) {
      return filename;
    }

    try {
      byte[] latin1Bytes = filename.getBytes(StandardCharsets.ISO_8859_1);
      String corrected = new String(latin1Bytes, StandardCharsets.UTF_8);

      if (corrected.contains("\uFFFD")) {
        return filename;
      }

      return corrected;
    } catch (Exception e) {
      log.debug("Encoding fix failed for '{}', returning original: {}", filename, e.getMessage());
      return filename;
    }
  }

  private static boolean isBlank(String str) {
    return str == null || str.trim().isEmpty();
  }
}
