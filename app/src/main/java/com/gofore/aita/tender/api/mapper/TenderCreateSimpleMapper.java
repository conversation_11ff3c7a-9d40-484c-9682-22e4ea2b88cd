package com.gofore.aita.tender.api.mapper;

import com.gofore.aita.tender.api.dto.TenderCreateSimpleDTO;
import com.gofore.aita.tender.domain.models.Tender;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public class TenderCreateSimpleMapper {

  public Tender toTender(TenderCreateSimpleDTO dto) {
    if (dto == null) {
      return null;
    }

    Tender tender = new Tender();
    tender.setTitle(dto.getTitle());
    tender.setSourceUrl(dto.getSourceUrl());
    tender.setDescription(dto.getDescription());

    return tender;
  }
}
