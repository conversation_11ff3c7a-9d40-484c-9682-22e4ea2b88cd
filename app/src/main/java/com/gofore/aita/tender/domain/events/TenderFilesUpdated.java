package com.gofore.aita.tender.domain.events;

import lombok.Getter;
import lombok.ToString;

/** An event that indicates that a Tender file has been created. */
@ToString
@Getter
public class TenderFilesUpdated extends TenderEvent {

  private final String fileId;

  public TenderFilesUpdated(String tenderId, String fileId) {
    super(tenderId);
    this.fileId = fileId;
  }

  enum Action {
    FILE_ADDED,
    FILE_REMOVED
  }
}
