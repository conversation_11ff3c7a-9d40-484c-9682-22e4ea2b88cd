package com.gofore.aita.tender.api.dto;

import com.gofore.aita.tender.domain.models.AnalysisResult;
import com.gofore.aita.tender.domain.models.WorkflowStatus;
import java.util.List;
import lombok.Data;

@Data
public class TenderDTO {
  private String id;
  private String title;
  private String sourceUrl;
  private String client;
  private String submissionDate;
  private String bindingDeadline;
  private String contractDuration;
  private String publicationDate;
  private String questionDeadline;
  private Float contractValue;
  private String maximumBudget;
  private String winningCriteria;
  private String weightingPriceQuality;
  private String deliveryLocation;
  private String description;
  private List<FileMetadataDTO> files;
  private AnalysisResult analysisResult;

  // User evaluation fields
  private String comment;
  private Integer rating;
  private Boolean isFavorite;

  // Lifecycle
  private TenderStatusDTO status;
  private WorkflowStatus workflowStatus;

  // System-generated information
  private String creationTime;
  private UserDTO createdBy;
  private String lastUpdatedTime;
  private UserDTO lastUpdatedBy;
}
