package com.gofore.aita.tender.data;

import com.gofore.aita.tender.domain.User;
import com.gofore.aita.tender.domain.models.FileMetadata;

public interface TenderRepositoryCustom {
  /**
   * Atomically adds a file to the tender with the given ID, ensuring no duplicate file (by
   * slugified name) exists. Also updates the tender's lastUpdatedTime and lastUpdatedBy.
   *
   * @param tenderId The tender's ID.
   * @param fileMetadata The file metadata to add.
   * @param user The user performing the update.
   * @return The added file metadata, or null if the update failed (e.g. duplicate exists).
   */
  FileMetadata addFileAtomic(String tenderId, FileMetadata fileMetadata, User user);

  /**
   * Atomically remove the file (identified by fileId) from the tender with the given tenderId.
   * Updates the tender’s lastUpdatedTime and lastUpdatedBy.
   *
   * @param tenderId the tender id
   * @param fileId the id of the file to remove
   * @param user the user performing the operation
   * @return true if the file was removed; false if no matching tender/file was found.
   */
  boolean deleteFileAtomic(String tenderId, String fileId, User user);
}
