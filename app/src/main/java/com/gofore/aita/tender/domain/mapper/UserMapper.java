package com.gofore.aita.tender.domain.mapper;

import com.gofore.aita.tender.domain.User;
import com.gofore.aita.tender.domain.models.ClientCredentials;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface UserMapper {

  @Mapping(source = "principalId", target = "userId")
  @Mapping(source = "principalName", target = "fullName")
  User toUser(ClientCredentials source);
}
