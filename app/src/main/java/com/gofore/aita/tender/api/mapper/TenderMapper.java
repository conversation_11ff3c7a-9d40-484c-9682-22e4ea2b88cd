package com.gofore.aita.tender.api.mapper;

import com.gofore.aita.tender.api.dto.TenderCreateDTO;
import com.gofore.aita.tender.api.dto.TenderDTO;
import com.gofore.aita.tender.api.dto.TenderStatusDTO;
import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.TenderStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TenderMapper {
  @Mapping(target = "files", source = "files", ignore = true)
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "analysisResult", ignore = true)
  @Mapping(target = "creationTime", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastUpdatedTime", ignore = true)
  @Mapping(target = "lastUpdatedBy", ignore = true)
  @Mapping(target = "version", ignore = true)
  @Mapping(
      target = "status",
      expression = "java(com.gofore.aita.tender.domain.models.TenderStatus.NEW)")
  Tender toTender(TenderCreateDTO source);

  TenderDTO toTenderDTO(Tender destination);

  default TenderStatusDTO map(TenderStatus status) {
    if (status == null) {
      return null;
    }

    return TenderStatusDTO.valueOf(status.name());
  }
}
