package com.gofore.aita.ankoe.controller;

import com.gofore.aita.ankoe.model.ANKOETender;
import com.gofore.aita.ankoe.model.TenderDetail;
import com.gofore.aita.ankoe.model.TenderFileInfos;
import com.gofore.aita.ankoe.model.TenderSearchRequest;
import com.gofore.aita.ankoe.service.ANKOEVergabeportalService;
import com.gofore.aita.tender.domain.models.FileMetadata;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/vergabeportal")
@Slf4j
public class ANKOEVergabeportalApiController {

  private final ANKOEVergabeportalService ankoeService;

  @Autowired
  public ANKOEVergabeportalApiController(ANKOEVergabeportalService ankoeService) {
    this.ankoeService = ankoeService;
  }

  @GetMapping("/tenders")
  public ResponseEntity<List<ANKOETender>> getTenders(
      @RequestParam(defaultValue = "1") int page,
      @RequestParam(defaultValue = "10") int pageSize,
      @RequestParam(required = false) String searchWords,
      @RequestParam(required = false) List<String> contractTypes,
      @RequestParam(required = false) List<String> regions,
      @RequestParam(required = false) List<String> formMainTypes,
      @RequestParam(required = false) List<String> cpvCodes,
      @RequestParam(defaultValue = "60") int publishDateType) {
    try {
      // Create a default search request with the provided parameters
      TenderSearchRequest searchRequest =
          ankoeService.createDefaultSearchRequest(
              page,
              pageSize,
              searchWords,
              contractTypes,
              regions,
              formMainTypes,
              cpvCodes,
              publishDateType);
      List<ANKOETender> ANKOETenders = ankoeService.getTenders(searchRequest);
      return ResponseEntity.ok(ANKOETenders);
    } catch (Exception e) {
      log.error("Error getting tenders: {}", e.getMessage(), e);
      return ResponseEntity.status(500).body(null);
    }
  }

  /**
   * Get detailed information about a tender
   *
   * @param id The ID of the tender to retrieve
   * @return Detailed tender information
   */
  @GetMapping("/tenders/{id}/details")
  public ResponseEntity<TenderDetail> getTenderDetails(@PathVariable String id) {
    try {
      TenderDetail tenderDetail = ankoeService.getTenderDetails(id);
      return ResponseEntity.ok(tenderDetail);
    } catch (Exception e) {
      log.error("Error getting tender details by ID: {}", e.getMessage(), e);
      return ResponseEntity.status(500).body(null);
    }
  }

  /**
   * Get file infos associated with a tender
   *
   * @param id The ID of the tender to retrieve files for
   * @return List of files associated with the tender
   */
  @GetMapping("/tenders/{id}/files")
  public ResponseEntity<List<TenderFileInfos>> getTenderFileInfos(@PathVariable String id) {
    try {
      List<TenderFileInfos> files = ankoeService.getTenderFileInfos(id);
      return ResponseEntity.ok(files);
    } catch (Exception e) {
      log.error("Error getting tender files by ID: {}", e.getMessage(), e);
      return ResponseEntity.status(500).body(null);
    }
  }

  /**
   * Downloads a file from Vergabeportal and stores it permanently
   *
   * @param tenderId The ID of the tender
   * @param fileId The ID of the file to download
   * @param fileName The name to use for the file
   * @param fileSize The size of the file in bytes
   * @return The file metadata for the downloaded file
   */
  @GetMapping("/tenders/downloadFile/{tenderId}/{storedTenderId}/{fileId}")
  public ResponseEntity<FileMetadata> downloadTenderFileAndAddToTender(
      @PathVariable String tenderId,
      @PathVariable String storedTenderId,
      @PathVariable String fileId,
      @RequestParam String fileName,
      @RequestParam Long fileSize) {
    try {
      FileMetadata fileMetadata =
          ankoeService.downloadTenderFileAndAddToTender(
              tenderId, storedTenderId, fileId, fileName, fileSize);
      return new ResponseEntity<>(fileMetadata, HttpStatus.CREATED);
    } catch (Exception e) {
      log.error("Error downloading file {} for tender {}: {}", fileId, tenderId, e.getMessage(), e);
      return ResponseEntity.status(500).build();
    }
  }
}
