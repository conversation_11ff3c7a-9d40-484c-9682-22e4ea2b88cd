package com.gofore.aita.ankoe.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/** Represents a tender from Vergabeportal API */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ANKOETender {
  private String id;
  private String source;
  private String formType;
  private String formMainType;
  private String contractType;
  private String procedureType;
  private String thresholdType;
  private String docNumber;
  private String title;
  private String shortDescription;
  private String content;
  private String submitDeadline;
  private String openingDate;
  private String publishedAt;
  private String tenderDocumentation;
  private String procurementUrl;
  private String documentationValue;
  private String documentationValueInfo;

  // Use @JsonSetter with Nulls.AS_EMPTY to handle null values as empty lists
  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<Region> regions = new ArrayList<>();

  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<String> cpvCodes = new ArrayList<>();

  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<ContractingAuthority> contractingAuthorities = new ArrayList<>();

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Region {
    private String regionCode;
    private String areaCode;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ContractingAuthority {
    private String name;
  }
}
