package com.gofore.aita.ankoe.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.java.net.cookiejar.JavaNetCookieJar;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ANKOEVergabeportalAuthService {

  @Value("${app.ankoe.username}")
  private String username;

  @Value("${app.ankoe.password}")
  private String password;

  private static final String LOGIN_URL = "https://identity.vergabeportal.at/Account/Login";
  private static final String TOKEN_URL = "https://identity.vergabeportal.at/connect/token";
  private static final String CLIENT_ID = "vergabeportal_angular";
  private static final String REDIRECT_URI =
      "https://www.vergabeportal.at/assets/pages/auth-callback.html";
  private static final String SCOPE =
      "openid profile vergabeportal_identity_api eabgabe_api vergabeportal_api tenderdata_api eingabeportal_api emailcommunication_api";

  private OkHttpClient httpClient;
  private final ObjectMapper objectMapper;

  @Getter private TokenInfo currentToken;

  public ANKOEVergabeportalAuthService() {
    // Configure OkHttp client with automatic cookie handling and redirect following
    this.httpClient =
        new OkHttpClient.Builder()
            .followRedirects(true)
            .followSslRedirects(true)
            .cookieJar(new JavaNetCookieJar(new java.net.CookieManager()))
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    this.objectMapper = new ObjectMapper();
  }

  /**
   * Authenticates with Vergabeportal and returns an access token
   *
   * @return The access token or null if authentication failed
   */
  public String authenticate() throws Exception {
    log.info("Starting authentication process with Vergabeportal");

    if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
      throw new RuntimeException(
          "Username and password must be provided via environment variables");
    }

    log.debug("Using username for authentication: {}", username);

    // Create a new client for every authentication attempt (to avoid cookie issues on re-auth)
    OkHttpClient freshClient =
        new OkHttpClient.Builder()
            .followRedirects(true)
            .followSslRedirects(true)
            .cookieJar(new JavaNetCookieJar(new java.net.CookieManager()))
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    // Use this client for this authentication session
    OkHttpClient originalClient = this.httpClient;
    this.httpClient = freshClient;

    try {
      // Step 1: Generate PKCE code verifier and challenge
      String codeVerifier = generateCodeVerifier();
      String codeChallenge = generateCodeChallenge(codeVerifier);
      String state = generateState();

      log.debug("Generated PKCE parameters - code_challenge: {}, state: {}", codeChallenge, state);

      // Step 2: Get request verification token (CSRF token)
      String requestToken = getRequestVerificationToken();
      log.debug(
          "Got request verification token: {}...",
          requestToken.substring(0, Math.min(20, requestToken.length())));

      // Step 3: Prepare the authorization URL with PKCE
      Map<String, String> authParams = new HashMap<>();
      authParams.put("client_id", CLIENT_ID);
      authParams.put("redirect_uri", REDIRECT_URI);
      authParams.put("response_type", "code");
      authParams.put("scope", SCOPE);
      authParams.put("state", state);
      authParams.put("code_challenge", codeChallenge);
      authParams.put("code_challenge_method", "S256");
      authParams.put("response_mode", "query");

      // Build the return URL for the login form
      String returnUrl = "/connect/authorize/callback?" + buildQueryString(authParams);
      log.debug("Return URL: {}", returnUrl);

      // Step 4: Submit login form to get authorization code
      String authCode = submitLoginForm(returnUrl, requestToken);
      log.debug(
          "Got authorization code: {}...", authCode.substring(0, Math.min(10, authCode.length())));

      // Step 5: Exchange the authorization code for a token
      return exchangeCodeForToken(authCode, codeVerifier);
    } catch (Exception e) {
      log.error("Authentication failed: {}", e.getMessage(), e);
      throw e;
    } finally {
      // Restore the original client
      this.httpClient = originalClient;
    }
  }

  /** Get a valid access token, refreshing if necessary */
  public String getAccessToken() throws Exception {
    // Check if we have a valid token
    if (currentToken != null && !currentToken.isExpired()) {
      log.debug("Using existing access token");
      return currentToken.getAccessToken();
    }

    // Get a new token
    log.info("Access token expired or not available, authenticating again");
    String accessToken = authenticate();

    return accessToken;
  }

  private String getRequestVerificationToken() throws IOException {
    Request request =
        new Request.Builder()
            .url(LOGIN_URL)
            .header(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            .header(
                "Accept",
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8")
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        throw new IOException("Failed to get login page: " + response.code());
      }

      String html = response.body().string();
      Pattern tokenPattern =
          Pattern.compile("name=\"__RequestVerificationToken\" type=\"hidden\" value=\"([^\"]+)\"");
      Matcher tokenMatcher = tokenPattern.matcher(html);

      if (tokenMatcher.find()) {
        return tokenMatcher.group(1);
      } else {
        log.debug(
            "Could not find request verification token in login page. Login page HTML: {}", html);
        throw new IOException("Could not find request verification token in login page");
      }
    }
  }

  private String submitLoginForm(String returnUrl, String requestToken) throws IOException {
    log.debug("Logging in as {}", username);

    // Replace + with %20 in the returnUrl to ensure proper OAuth2 scope encoding
    returnUrl = returnUrl.replace("+", "%20");

    // URL encode the return URL for the query parameter
    String encodedReturnUrl = URLEncoder.encode(returnUrl, StandardCharsets.UTF_8);
    String loginUrlWithReturn = LOGIN_URL + "?ReturnUrl=" + encodedReturnUrl;

    // Build form data
    FormBody formBody =
        new FormBody.Builder()
            .add("ReturnUrl", returnUrl)
            .add("Username", username)
            .add("Password", password)
            .add("button", "login")
            .add("__RequestVerificationToken", requestToken)
            .build();

    // Create request with headers
    Request request =
        new Request.Builder()
            .url(loginUrlWithReturn)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .header(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            .header("Origin", "https://identity.vergabeportal.at")
            .header("Referer", loginUrlWithReturn)
            .header(
                "Accept",
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8")
            .post(formBody)
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
      // The final URL after redirects should contain the authorization code
      String finalUrl = response.request().url().toString();
      log.debug("Final URL after redirects: {}", finalUrl);

      // Extract authorization code from URL
      Pattern codePattern = Pattern.compile("[?&]code=([^&]+)");
      Matcher codeMatcher = codePattern.matcher(finalUrl);

      if (codeMatcher.find()) {
        return codeMatcher.group(1);
      }

      // If not found in URL, try to find in response body (some flows might return it there)
      String responseBody = response.body().string();
      log.debug("Response body length: {}", responseBody.length());

      // Look for code in response body
      codeMatcher = codePattern.matcher(responseBody);
      if (codeMatcher.find()) {
        return codeMatcher.group(1);
      }

      // Look for code in meta refresh tag - common in OAuth2 flows
      Pattern refreshPattern =
          Pattern.compile("<meta http-equiv=\"refresh\" content=\"[^\"]*url=([^\"]+)\"");
      Matcher refreshMatcher = refreshPattern.matcher(responseBody);
      if (refreshMatcher.find()) {
        String refreshUrl = refreshMatcher.group(1);
        log.debug("Found refresh URL: {}", refreshUrl);

        // Check if the refresh URL contains the code
        codeMatcher = codePattern.matcher(refreshUrl);
        if (codeMatcher.find()) {
          return codeMatcher.group(1);
        }
      }

      // Check for error messages
      if (responseBody.contains("invalid_scope") || responseBody.contains("alert-danger")) {
        Pattern errorPattern =
            Pattern.compile(
                "<div class=\"alert alert-danger[^>]*>\\s*([^<]+)\\s*<strong>\\s*<em>\\s*:?\\s*([^<]+)\\s*</em>\\s*</strong>");
        Matcher errorMatcher = errorPattern.matcher(responseBody);
        if (errorMatcher.find()) {
          String errorType = errorMatcher.group(1).trim();
          String errorMsg = errorMatcher.group(2).trim();
          throw new IOException("Authentication error: " + errorType + " - " + errorMsg);
        }
      }

      throw new IOException("Could not extract authorization code from response");
    }
  }

  private String exchangeCodeForToken(String authCode, String codeVerifier) throws IOException {
    log.debug("Exchanging authorization code for token");

    // Build form data
    FormBody formBody =
        new FormBody.Builder()
            .add("client_id", CLIENT_ID)
            .add("code", authCode)
            .add("redirect_uri", REDIRECT_URI)
            .add("code_verifier", codeVerifier)
            .add("grant_type", "authorization_code")
            .build();

    // Create request
    Request request =
        new Request.Builder()
            .url(TOKEN_URL)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .header(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            .post(formBody)
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        throw new IOException(
            "Failed to exchange code for token: "
                + response.code()
                + " - "
                + response.body().string());
      }

      String responseBody = response.body().string();
      JsonNode jsonNode = objectMapper.readTree(responseBody);

      String accessToken = jsonNode.get("access_token").asText();
      long expiresIn = jsonNode.get("expires_in").asLong();

      // Store token information
      this.currentToken =
          new TokenInfo(
              accessToken,
              jsonNode.has("refresh_token") ? jsonNode.get("refresh_token").asText() : null,
              Instant.now().plusSeconds(expiresIn));

      log.info("Successfully obtained access token, expires in {} seconds", expiresIn);
      return accessToken;
    }
  }

  // Helper methods

  private String generateCodeVerifier() {
    SecureRandom secureRandom = new SecureRandom();
    byte[] codeVerifier = new byte[32];
    secureRandom.nextBytes(codeVerifier);
    return Base64.getUrlEncoder().withoutPadding().encodeToString(codeVerifier);
  }

  private String generateCodeChallenge(String codeVerifier) {
    try {
      MessageDigest digest = MessageDigest.getInstance("SHA-256");
      byte[] hash = digest.digest(codeVerifier.getBytes(StandardCharsets.UTF_8));
      return Base64.getUrlEncoder().withoutPadding().encodeToString(hash);
    } catch (Exception e) {
      throw new RuntimeException("Failed to generate code challenge", e);
    }
  }

  private String generateState() {
    SecureRandom secureRandom = new SecureRandom();
    byte[] state = new byte[16];
    secureRandom.nextBytes(state);
    return Base64.getUrlEncoder().withoutPadding().encodeToString(state);
  }

  private String buildQueryString(Map<String, String> params) {
    StringBuilder result = new StringBuilder();
    boolean first = true;

    for (Map.Entry<String, String> entry : params.entrySet()) {
      if (first) {
        first = false;
      } else {
        result.append("&");
      }

      result.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8));
      result.append("=");

      String value = entry.getValue();
      // Special handling for scope parameter
      if ("scope".equals(entry.getKey())) {
        // Use %20 for spaces instead of + in the scope parameter
        value = URLEncoder.encode(value, StandardCharsets.UTF_8).replace("+", "%20");
      } else {
        value = URLEncoder.encode(value, StandardCharsets.UTF_8);
      }

      result.append(value);
    }

    return result.toString();
  }

  /** Class to track token information and expiration */
  private static class TokenInfo {
    private final String accessToken;
    private final String refreshToken;
    private final Instant expiresAt;

    public TokenInfo(String accessToken, String refreshToken, Instant expiresAt) {
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
      this.expiresAt = expiresAt;
    }

    public String getAccessToken() {
      return accessToken;
    }

    public String getRefreshToken() {
      return refreshToken;
    }

    public boolean isExpired() {
      return Instant.now().isAfter(expiresAt);
    }
  }
}
