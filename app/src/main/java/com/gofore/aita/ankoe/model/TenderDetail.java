package com.gofore.aita.ankoe.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/** Represents a detailed tender from Vergabeportal API */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TenderDetail {
  private String id;
  private String source;
  private String sourceDocNumber;
  private String formType;
  private String formMainType;
  private String contractType;
  private String procedureType;
  private String thresholdType;
  private String docNumber;
  private String title;
  private String shortDescription;
  private String content;
  private String submitDeadline;
  private String openingDate;
  private String publishedAt;
  private String procurementUrl;
  private String tenderDocumentation;
  private String documentationValue;
  private String documentationValueInfo;
  private TotalValue totalValue;
  private String totalValueInfo;
  private String durationOfContract;
  private String decisionCriteria;
  private String ojHeading;
  private String ojNumber;
  private String origLang;
  private String bidType;
  private String mainActivity;
  private String tedNoDocOjsRefNumber;

  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<String> cpvCodes = new ArrayList<>();

  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<Region> regions = new ArrayList<>();

  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<String> nutsCodes = new ArrayList<>();

  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<ContractingAuthority> contractingAuthorities = new ArrayList<>();

  @JsonSetter(nulls = Nulls.AS_EMPTY)
  private List<Contractor> awardContractors = new ArrayList<>();

  private Boolean isOgdSource;

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TotalValue {
    private Float amount;
    private String currency;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Region {
    private String regionCode;
    private String areaCode;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ContractingAuthority {
    private String domain;
    private Boolean isMain;
    private String type;
    private String nutsCode;
    private String name;
    private String nationalRegistrationNumber;
    private String postalAddress;
    private String town;
    private String postalCode;
    private String country;
    private String contactPerson;
    private String phone;
    private String fax;
    private String email;
    private String internetAddress;
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Contractor {
    // Add fields as needed based on API responses
    private String name;
    private String country;
  }
}
