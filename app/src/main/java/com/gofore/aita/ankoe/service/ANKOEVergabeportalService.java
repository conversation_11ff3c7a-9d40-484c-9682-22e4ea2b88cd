package com.gofore.aita.ankoe.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gofore.aita.ankoe.model.*;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.FileStorageService;
import com.gofore.aita.tender.domain.User;
import com.gofore.aita.tender.domain.exceptions.BadRequestException;
import com.gofore.aita.tender.domain.models.FileMetadata;
import com.gofore.aita.tender.domain.models.Tender;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ANKOEVergabeportalService {

  private static final String VERGABEPORTAL_API_URL =
      "https://gateway.vergabeportal.at/api/v1/td/client/tender/search";
  private static final String TENDER_DETAIL_API_URL =
      "https://gateway.vergabeportal.at/api/v1/td/client/tender/";
  private static final String TENDER_FILES_API_URL =
      "https://gateway.vergabeportal.at/api/v1/ep/client/noticefile";
  private static final String TENDER_FILE_DOWNLOAD_API_URL =
      "https://gateway.vergabeportal.at/api/v1/ep/client/noticefile/download";
  private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

  private final ANKOEVergabeportalAuthService authService;
  private final ObjectMapper objectMapper;
  private final OkHttpClient httpClient;
  private final FileStorageService fileStorageService;
  private final TenderRepository tenderRepository;

  @Autowired
  public ANKOEVergabeportalService(
      ANKOEVergabeportalAuthService authService,
      ObjectMapper objectMapper,
      FileStorageService fileStorageService,
      TenderRepository tenderRepository) {
    this.authService = authService;
    this.objectMapper = objectMapper;
    this.fileStorageService = fileStorageService;
    this.tenderRepository = tenderRepository;

    // Initialize OkHttpClient
    this.httpClient =
        new OkHttpClient.Builder()
            .followRedirects(true)
            .followSslRedirects(true)
            .connectTimeout(300, TimeUnit.SECONDS)
            .readTimeout(300, TimeUnit.SECONDS)
            .build();
  }

  /**
   * Search for tenders with a custom search request
   *
   * @param searchRequest The search request with all parameters
   * @return List of tenders matching the search criteria
   * @throws Exception If the API call fails
   */
  public List<ANKOETender> getTenders(TenderSearchRequest searchRequest) throws Exception {
    // Get valid access token
    String accessToken = authService.getAccessToken();

    // Convert search request to JSON
    String requestJson = objectMapper.writeValueAsString(searchRequest);

    // Build the request
    okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(requestJson, JSON);
    Request request =
        new Request.Builder()
            .url(VERGABEPORTAL_API_URL)
            .post(requestBody)
            .header("Authorization", "Bearer " + accessToken)
            .header("Accept", "application/json, text/plain, */*")
            .header("Content-Type", "application/json")
            .header("Origin", "https://www.vergabeportal.at")
            .header("Referer", "https://www.vergabeportal.at/")
            .header("mandanttoken", "AT")
            .header(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
      String responseBody = response.body() != null ? response.body().string() : "";

      if (!response.isSuccessful()) {
        log.error(
            "API request failed: {} - {} - Response: {}",
            response.code(),
            response.message(),
            responseBody);
        throw new Exception("API request failed with status " + response.code());
      }

      // Clean the response body of any HTML markup
      String cleanedResponseBody = cleanResponseBody(responseBody);

      // The API returns an array of tenders directly, not a wrapped response
      List<ANKOETender> ANKOETenders =
          objectMapper.readValue(
              cleanedResponseBody,
              objectMapper.getTypeFactory().constructCollectionType(List.class, ANKOETender.class));

      if (ANKOETenders == null || ANKOETenders.isEmpty()) {
        log.error("Failed to retrieve tenders or no tenders found");
        throw new Exception("Failed to retrieve tenders or no tenders found");
      }

      log.info("Retrieved {} tenders", ANKOETenders.size());
      return ANKOETenders;
    } catch (Exception e) {
      log.error("Error accessing Vergabeportal API", e);
      throw e;
    }
  }

  /**
   * Get detailed information about a tender
   *
   * @param tenderId The ID of the tender to retrieve
   * @return Detailed tender information
   * @throws Exception If the API call fails
   */
  public TenderDetail getTenderDetails(String tenderId) throws Exception {
    // Get valid access token
    String accessToken = authService.getAccessToken();

    // Build the request to get detailed tender information
    String url = TENDER_DETAIL_API_URL + tenderId;
    Request request =
        new Request.Builder()
            .url(url)
            .get()
            .header("Authorization", "Bearer " + accessToken)
            .header("Accept", "application/json, text/plain, */*")
            .header("Origin", "https://www.vergabeportal.at")
            .header("Referer", "https://www.vergabeportal.at/")
            .header("mandanttoken", "AT")
            .header(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
      log.debug(
          "Vergabeportal API response code for tender details {}: {}", tenderId, response.code());

      if (!response.isSuccessful()) {
        log.error(
            "API request failed: {} - {} for URL {}", response.code(), response.message(), url);
        throw new Exception("API request failed with status " + response.code());
      }

      ResponseBody responseBody = response.body();
      if (responseBody == null) {
        log.error("Empty response body for tender details {}", tenderId);
        throw new Exception("Empty response body for tender details");
      }

      String responseBodyString = responseBody.string();
      log.debug(
          "Vergabeportal API response for tender details {}: {}", tenderId, responseBodyString);

      // Clean and parse the response
      String cleanedResponseBody = cleanResponseBody(responseBodyString);
      TenderDetail tenderDetails = objectMapper.readValue(cleanedResponseBody, TenderDetail.class);

      if (tenderDetails == null) {
        log.error("Failed to retrieve tender details for tender {}", tenderId);
        throw new Exception("Failed to retrieve tender details for tender " + tenderId);
      }

      log.debug(
          "Retrieved details for tender: {} - {}", tenderDetails.getTitle(), tenderDetails.getId());

      return tenderDetails;
    } catch (Exception e) {
      log.error("Error getting tender details by ID: {}", e.getMessage(), e);
      throw e;
    }
  }

  /**
   * Get file infos associated with a tender
   *
   * @param tenderId The ID of the tender to retrieve files for
   * @return List of files associated with the tender
   * @throws Exception If the API call fails
   */
  public List<TenderFileInfos> getTenderFileInfos(String tenderId) throws Exception {
    // Get valid access token
    String accessToken = authService.getAccessToken();

    // Build the request to get tender file infos
    String url = TENDER_FILES_API_URL + "?TenderId=" + tenderId;
    Request request =
        new Request.Builder()
            .url(url)
            .get()
            .header("Authorization", "Bearer " + accessToken)
            .header("Accept", "application/json, text/plain, */*")
            .header("Origin", "https://www.vergabeportal.at")
            .header("Referer", "https://www.vergabeportal.at/")
            .header("mandanttoken", "AT")
            .header(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
      log.debug(
          "Vergabeportal API response code for tender files {}: {}", tenderId, response.code());

      // Handle 204 No Content response specially - it means no files are available
      if (response.code() == 204) {
        log.info("No files found for tender {}", tenderId);
        return List.of(); // Return empty list
      }

      if (!response.isSuccessful()) {
        log.error("API request failed: {} - {}", response.code(), response.message());
        throw new Exception("API request failed with status " + response.code());
      }

      // For successful responses with content
      ResponseBody responseBody = response.body();
      if (responseBody == null || responseBody.contentLength() == 0) {
        // Handle empty but successful responses
        log.info("Empty response for tender files {}", tenderId);
        return List.of();
      }

      String responseBodyString = responseBody.string();
      log.debug("Vergabeportal API response for tender files {}: {}", tenderId, responseBodyString);

      // If response is empty JSON array or empty string, return empty list
      if (responseBodyString == null
          || responseBodyString.isEmpty()
          || responseBodyString.equals("[]")
          || responseBodyString.equals("{}")) {
        return List.of();
      }

      // Clean and parse the response
      String cleanedResponseBody = cleanResponseBody(responseBodyString);

      // The API returns an array of files
      List<TenderFileInfos> tenderFileInfos =
          objectMapper.readValue(
              cleanedResponseBody,
              objectMapper
                  .getTypeFactory()
                  .constructCollectionType(List.class, TenderFileInfos.class));

      if (tenderFileInfos == null || tenderFileInfos.isEmpty()) {
        log.info("No files available for tender {}", tenderId);
      } else {
        log.info("Found {} files for tender {}", tenderFileInfos.size(), tenderId);
      }

      return tenderFileInfos;
    } catch (Exception e) {
      log.error("Error getting tender files by ID: {}", e.getMessage(), e);
      throw e;
    }
  }

  /**
   * Downloads a file from Vergabeportal and stores it permanently in our system
   *
   * @param tenderId The ID of the tender
   * @param fileId The ID of the file to download
   * @param fileName The name to use for the file
   * @param fileSize The size of the file in bytes
   * @return The file metadata for the downloaded file
   * @throws Exception If the API call fails
   */
  public FileMetadata downloadTenderFileAndAddToTender(
      String tenderId, String storedTenderId, String fileId, String fileName, Long fileSize)
      throws Exception {

    log.info("Downloading file {} for tender {}", fileId, tenderId);

    // Get valid access token
    String accessToken = authService.getAccessToken();

    // Build the request to download the file
    String url = TENDER_FILE_DOWNLOAD_API_URL + "?TenderId=" + tenderId + "&FileId=" + fileId;
    Request request =
        new Request.Builder()
            .url(url)
            .get()
            .header("Authorization", "Bearer " + accessToken)
            .header("Accept", "application/json, text/plain, */*")
            .header("Origin", "https://www.vergabeportal.at")
            .header("Referer", "https://www.vergabeportal.at/")
            .header("mandanttoken", "AT")
            .header(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            .build();

    // Execute request to download file
    try (Response response = httpClient.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        log.error("API request failed: {} - {}", response.code(), response.message());
        throw new Exception("API request failed with status " + response.code());
      }

      ResponseBody responseBody = response.body();
      if (responseBody == null) {
        log.error("Empty response body for file download");
        throw new Exception("Empty response body for file download");
      }

      // Read the file content into memory
      byte[] fileContent = responseBody.bytes();

      // Now store the file permanently using the FileStorageService
      try (InputStream inputStream = new ByteArrayInputStream(fileContent)) {

        FileMetadata fileMetadata = null;
        FileMetadata result = null;

        // unzip if zip file
        if (fileName.endsWith(".zip")) {
          log.info("Unzip file: {}", fileName);
          List<FileToStore> files = unzipFile(inputStream);

          for (FileToStore file : files) {
            // Store the file in the system
            fileMetadata =
                fileStorageService.storeFile(
                    storedTenderId, file.fileName, file.inputStream, file.fileSize);

            // Add the file to an existing tender
            result =
                tenderRepository.addFileAtomic(
                    storedTenderId, fileMetadata, new User("1", "AITA tender service"));
          }
        } else {
          // Store the file in the system
          fileMetadata =
              fileStorageService.storeFile(storedTenderId, fileName, inputStream, fileSize);

          // Add the file to an existing tender
          result =
              tenderRepository.addFileAtomic(
                  storedTenderId, fileMetadata, new User("1", "AITA tender service"));
        }

        if (result == null) {
          fileStorageService.delete(fileMetadata);
          throw new BadRequestException("File already exists or tender not found.");
        }

        return result;
      } catch (Exception e) {
        log.error("Error storing file {} for tender {}: {}", fileId, tenderId, e.getMessage(), e);
        throw e;
      }
    } catch (Exception e) {
      log.error("Error downloading file {} for tender {}: {}", fileId, tenderId, e.getMessage(), e);
      throw e;
    }
  }

  public void downloadAllTenderFilesAndAddToTender(
      List<TenderFileInfos> fileInfos, String tenderId, String storedTenderId) throws Exception {
    for (TenderFileInfos fileInfo : fileInfos) {
      if (fileInfo.getActive()) {

        // skip "Gesamt-zip" file
        String name = fileInfo.getName();
        if (name.contains("Gesamt") && name.contains(".zip")) {
          log.info("Skipping file: {}", name);
          continue;
        }

        log.info("Downloading active file {}", name);

        FileMetadata fileMetadata =
            downloadTenderFileAndAddToTender(
                tenderId, storedTenderId, fileInfo.getId(), name, fileInfo.getFileSize());

        if (fileMetadata != null) {
          log.info("Successfully downloaded and added file: {}", name);
        } else {
          log.error("Failed to download file: {}", name);
        }
      }
    }
  }

  private static List<FileToStore> unzipFile(InputStream inputStream) throws IOException {
    List<FileToStore> unzippedFiles = new ArrayList<>();

    // Create a ZipInputStream with explicit encoding to handle non-UTF-8 filenames
    try (ZipInputStream zis =
        new ZipInputStream(inputStream, java.nio.charset.Charset.forName("CP437"))) {
      ZipEntry zipEntry;
      byte[] buffer = new byte[8192]; // Buffer for reading the file content

      while ((zipEntry = zis.getNextEntry()) != null) {
        try {
          // Skip directories (folder entries)
          if (zipEntry.isDirectory()) {
            log.info("Skipping directory: {}", zipEntry.getName());
            continue;
          }

          log.info("Unzipping file: {}", zipEntry.getName());

          // Create a ByteArrayOutputStream to store the file content
          ByteArrayOutputStream baos = new ByteArrayOutputStream();
          int len;
          while ((len = zis.read(buffer)) > 0) {
            baos.write(buffer, 0, len);
          }

          // Convert to ByteArrayInputStream for storage
          byte[] bytes = baos.toByteArray();
          InputStream zipEntryInputStream = new ByteArrayInputStream(bytes);

          // Handle potentially invalid UTF-8 characters in filename
          String safeName = getSafeFileName(zipEntry.getName());

          long fileSize = bytes.length;
          log.info("File size: {}", fileSize);

          unzippedFiles.add(new FileToStore(zipEntryInputStream, safeName, fileSize));
        } catch (Exception e) {
          // Log the error but continue processing other entries
          log.warn("Error processing ZIP entry: {}", e.getMessage(), e);
        } finally {
          // Close the current entry before moving to the next one
          zis.closeEntry();
        }
      }
    }
    return unzippedFiles;
  }

  /**
   * Sanitizes a filename that might contain invalid UTF-8 characters
   *
   * @param originalName The original filename from the ZIP entry
   * @return A safe filename with invalid characters replaced
   */
  private static String getSafeFileName(String originalName) {
    if (originalName == null) {
      return "unknown_file";
    }

    try {
      // Test if the string is valid UTF-8
      return originalName;
    } catch (Exception e) {
      // If we encounter encoding issues, try a different approach
      byte[] bytes = originalName.getBytes(StandardCharsets.ISO_8859_1);
      String fallbackName = new String(bytes, StandardCharsets.UTF_8);

      // Replace any remaining invalid characters
      return fallbackName.replaceAll("[^\\p{ASCII}]", "_");
    }
  }

  private static class FileToStore {
    InputStream inputStream;
    String fileName;
    long fileSize;

    public FileToStore(InputStream inputStream, String fileName, long fileSize) {
      this.inputStream = inputStream;
      this.fileName = fileName;
      this.fileSize = fileSize;
    }
  }

  /** Utility method to clean HTML markup from API responses */
  private String cleanResponseBody(String responseBody) {
    // remove all HTML tags
    return responseBody.replaceAll("<[^>]*>", "");
  }

  /** Creates a default search request with commonly used parameters */
  public TenderSearchRequest createDefaultSearchRequest(
      int page,
      int pageSize,
      String searchWords,
      List<String> contractTypes,
      List<String> regions,
      List<String> formMainTypes,
      List<String> cpvCodes,
      int publishDateType) {

    // Create pagination
    Pagination pagination = new Pagination();
    pagination.setCurrentPage(page);
    pagination.setPageSize(pageSize);

    // Create search criteria
    SearchCriteria criteria = new SearchCriteria();
    criteria.setSearchWords(searchWords != null ? searchWords : "");
    criteria.setExcludeWords(null);
    criteria.setContractingAuthority(null);

    // Set contract types (default to services and competition if not provided)
    if (contractTypes != null && !contractTypes.isEmpty()) {
      criteria.setContractTypes(contractTypes);
    } else {
      criteria.setContractTypes(List.of("contract_type_services", "contract_type_competition"));
    }

    // Set search method
    criteria.setSearchMethod(0);

    // Set regions (default to Austria regions if not provided)
    if (regions != null && !regions.isEmpty()) {
      criteria.setRegions(regions);
    } else {
      criteria.setRegions(
          List.of(
              "Country_AT",
              "Region_AT_11",
              "Region_AT_12",
              "Region_AT_13",
              "Region_AT_21",
              "Region_AT_22",
              "Region_AT_31",
              "Region_AT_32",
              "Region_AT_33",
              "Region_AT_34"));
    }

    // Set form main types (default to notices if not provided)
    if (formMainTypes != null && !formMainTypes.isEmpty()) {
      criteria.setFormMainTypes(formMainTypes);
    } else {
      criteria.setFormMainTypes(
          List.of(
              "notice_main_prior_information_notice",
              "notice_main_current_tenders",
              "notice_main_correction"));
    }

    // Set publish date type (default to last 60 days)
    criteria.setPublishDateType(publishDateType);
    criteria.setPublishDateFrom(null);
    criteria.setPublishDateTo(null);

    // Set submit date properties
    criteria.setSubmitDateType(0);
    criteria.setSubmitDateFrom(null);
    criteria.setSubmitDateTo(null);

    // Set CPV codes if provided or use the default IT services CPV codes
    if (cpvCodes != null && !cpvCodes.isEmpty()) {
      criteria.setCpvCodes(cpvCodes);
    } else {
      // Default IT services CPV codes
      criteria.setCpvCodes(
          List.of(
        "72000000",     // IT-Dienste: Beratung, Software-Entwicklung, Internet und Hilfestellung
                  "72210000",     // Programmierung von Softwarepaketen
                  "72211000",     // Programmierung von System- und Anwendersoftware
                  "72212000",     // Programmierung von Anwendersoftware
                  "72212100",     // Entwicklung von branchenspezifischer Software
                  "72212140",     // Entwicklung von Software für Eisenbahnleitsysteme
                  "72212150",     // Entwicklung von Industrieprozesssteuerungssoftware
                  "72212160",     // Entwicklung von Bibliothekensoftware
                  "72212200",     // Entwicklung von Vernetzungs-, Internet- und Intranetsoftware
                  "72212220",     // Entwicklung von Internet- und Intranetsoftware
                  "72212222",     // Entwicklung von Webserversoftware
                  "72212300",     // Entwicklung von Software für Dokumentenerstellung, Zeichnen, Bildverarbeitung, Terminplanung und Produktivität
                  "72212311",     // Entwicklung von Dokumentenverwaltungssoftware
                  "72212312",     // Entwicklung von Software für das elektronische Publizieren
                  "72212400",     // Entwicklung von Software für Geschäftstransaktionen und persönliche Arbeitsabläufe
                  "72212412",     // Entwicklung von Steuersoftware
                  "72212430",     // Entwicklung von Lagerverwaltungssoftware
                  "72212442",     // Entwicklung von Finanzsystemsoftware
                  "72212443",     // Entwicklung von Buchhaltungssoftware
                  "72212445",     // Entwicklung von Software für das Kundenbeziehungsmanagement (CRM)
                  "72212451",     // Entwicklung von Software für die Unternehmensressourcenplanung (ERP)
                  "72212500",     // Entwicklung von Kommunikations- und Multimedia-Software
                  "72212771",     // Entwicklung von allgemeinen Dienstprogrammen
                  "72212920",     // Entwicklung von Büroautomatisierungssoftware
                  "72212960",     // Entwicklung von Treiber- und Systemsoftware
                  "72212982",     // Entwicklung von Konfigurationsverwaltungssoftware
                  "72220000",     // Systemberatung und technische Beratung
                  "72221000",     // Beratung im Bereich Unternehmensanalyse
                  "72222300",     // Informationstechnologiedienste
                  "72224000",     // Beratung im Bereich Projektleitung
                  "72224100",     // Planung im Bereich Systemimplementierung
                  "72224200",     // Planung im Bereich Systemqualitätssicherung
                  "72227000",     // Beratung im Bereich Software-Integration
                  "72230000",     // Entwicklung von kundenspezifischer Software
                  "72231000",     // Entwicklung von Software für militärische Anwendungen
                  "72232000",     // Entwicklung von Transaktionsverarbeitungssoftware und kundenspezifischer Software
                  "72240000",     // Systemanalyse und Programmierung
                  "72241000",     // Festlegung kritischer Planungsziele
                  "72242000",     // Entwurfsmodellierung
                  "72243000",     // Programmierung
                  "72244000",     // Prototyping
                  "72245000",     // Vertragliche Systemanalyse und Programmierung
                  "72246000",     // Systemberatung
                  "72260000",     // Dienstleistungen in Verbindung mit Software
                  "72262000",     // Software-Entwicklung
                  "72263000",     // Software-Implementierung
                  "72264000",     // Software-Reproduktion
                  "72265000",     // Software-Konfiguration
                  "72266000",     // Software-Beratung
                  "72267000",     // Software-Wartung und -Reparatur
                  "72267100",     // Wartung von Informationstechnologiesoftware
                  "72267200",     // Reparatur von Informationstechnologiesoftware
                  "72268000",     // Bereitstellung von Software
                  "72420000",     // Internet-Entwicklung
                  "72421000",     // Entwicklung von Internet- oder Intranet-Kundenanwendungen
                  "72422000"      // Entwicklung von Internet- oder Intranet-Serveranwendungen
          ));
    }

    // Set threshold type and other filter arrays
    criteria.setThresholdType(null);
    criteria.setProcedureTypes(List.of());
    criteria.setFilterContractingAuthorities(List.of());
    criteria.setFilterCpvCodes(List.of());
    criteria.setFilterProcedureTypes(List.of());
    criteria.setFilterRegions(List.of());
    criteria.setFilterFormMainTypes(List.of());

    // Create sort with default value (publish date descending)
    Sort sort = new Sort();
    sort.setLabel("publishDate_desc");
    sort.setValue(4);

    // Create and return the complete search request
    TenderSearchRequest searchRequest = new TenderSearchRequest();
    searchRequest.setPagination(pagination);
    searchRequest.setCriteria(criteria);
    searchRequest.setSort(sort);

    return searchRequest;
  }

  /** Maps a TenderDetail from the Vergabeportal API to our domain Tender model */
  public Tender mapToDomainTender(TenderDetail tenderDetail) {
    Tender domainTender = new Tender();
    domainTender.setExternalId(tenderDetail.getId());

    // Map fields from TenderDetail to our domain Tender
    domainTender.setTitle(tenderDetail.getTitle());
    domainTender.setDescription(tenderDetail.getContent());

    // Set client/contracting authority from the first authority if available
    Optional.ofNullable(tenderDetail.getContractingAuthorities())
        .filter(list -> !list.isEmpty())
        .map(list -> list.get(0))
        .map(TenderDetail.ContractingAuthority::getName)
        .ifPresent(domainTender::setClient);

    // Set dates if available
    domainTender.setSubmissionDate(tenderDetail.getSubmitDeadline());
    domainTender.setPublicationDate(tenderDetail.getPublishedAt());

    // Set contract duration
    domainTender.setContractDuration(tenderDetail.getDurationOfContract());

    // Set URL reference
    domainTender.setSourceUrl("https://www.vergabeportal.at/tender/" + tenderDetail.getId());

    // Set contract value if available
    Optional.ofNullable(tenderDetail.getTotalValue())
        .map(TenderDetail.TotalValue::getAmount)
        .ifPresent(domainTender::setContractValue);

    // Set winning criteria
    domainTender.setWinningCriteria(tenderDetail.getDecisionCriteria());

    // Set creation timestamp
    domainTender.setCreationTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
    domainTender.setCreatedBy(new User("1", "AITA tender service"));

    return domainTender;
  }
}
