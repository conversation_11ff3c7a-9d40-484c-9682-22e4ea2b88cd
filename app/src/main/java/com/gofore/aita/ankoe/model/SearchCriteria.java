package com.gofore.aita.ankoe.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;

/** Represents search criteria for tender search */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchCriteria {
  private String searchWords;
  private String excludeWords;
  private String contractingAuthority;
  private List<String> contractTypes;
  private int searchMethod;
  private List<String> regions;
  private List<String> formMainTypes;
  private int publishDateType;
  private String publishDateFrom;
  private String publishDateTo;
  private int submitDateType;
  private String submitDateFrom;
  private String submitDateTo;
  private List<String> cpvCodes;
  private String thresholdType;
  private List<String> procedureTypes;
  private List<String> filterContractingAuthorities;
  private List<String> filterCpvCodes;
  private List<String> filterProcedureTypes;
  private List<String> filterRegions;
  private List<String> filterFormMainTypes;
}
