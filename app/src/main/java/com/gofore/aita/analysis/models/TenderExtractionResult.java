package com.gofore.aita.analysis.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
  "client",
  "submissionDate",
  "bindingDeadline",
  "contractDuration",
  "publicationDate",
  "questionDeadline",
  "contractValue",
  "maximumBudget",
  "winningCriteria",
  "weightingPriceQuality",
  "deliveryLocation",
  "rating"
})
@Data
public class TenderExtractionResult {
  private String client;
  private String submissionDate;
  private String bindingDeadline;
  private String contractDuration;
  private String publicationDate;
  private String questionDeadline;
  private Float contractValue;
  private String maximumBudget;
  private String winningCriteria;
  private String weightingPriceQuality;
  private String deliveryLocation;
  private Integer rating;
  private String aiRawMessage;
}
