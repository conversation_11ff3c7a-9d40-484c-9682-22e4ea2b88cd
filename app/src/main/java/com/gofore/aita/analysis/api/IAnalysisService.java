package com.gofore.aita.analysis.api;

import com.gofore.aita.analysis.models.AnalysisResult;
import com.gofore.aita.analysis.models.TenderExtractionResult;
import java.util.List;

public interface IAnalysisService {
  /**
   * Analyzes tender information using the default prompt from system configuration.
   *
   * @param tenderInformation The tender information to analyze
   * @return The analysis result
   */
  AnalysisResult analyzeTender(String tenderInformation);

  /**
   * Analyzes tender information using a custom prompt.
   *
   * @param tenderInformation The tender information to analyze
   * @param customPrompt The custom prompt to use for analysis
   * @return The analysis result
   */
  AnalysisResult analyzeTender(String tenderInformation, String customPrompt);

  /**
   * Extracts structured tender fields from tender information and file content.
   *
   * @param tenderInformation The tender information to extract fields from
   * @return The extracted tender fields
   */
  TenderExtractionResult extractTenderFields(String tenderInformation);

  /**
   * Combines extracted structured tender fields from tender information and file content.
   *
   * @param extractionResults The extracted tender fields to combine
   * @return The extracted tender fields
   */
  TenderExtractionResult combineTenderFields(List<TenderExtractionResult> extractionResults);

  /**
   * Combines analysis results from tender information
   *
   * @param analysisResults The list of analysis results to combine
   * @return The analysis of the tender
   */
  com.gofore.aita.analysis.models.AnalysisResult combineAnalysisResults(
      List<com.gofore.aita.analysis.models.AnalysisResult> analysisResults);
}
