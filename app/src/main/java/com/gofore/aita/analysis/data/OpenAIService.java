package com.gofore.aita.analysis.data;

import com.azure.ai.openai.OpenAIAsyncClient;
import com.azure.ai.openai.models.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gofore.aita.analysis.api.IAnalysisService;
import com.gofore.aita.analysis.models.AnalysisResult;
import com.gofore.aita.analysis.models.TenderExtractionResult;
import com.gofore.aita.core.domain.SystemConfigurationService;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class OpenAIService implements IAnalysisService {

  private final Logger logger = LoggerFactory.getLogger(OpenAIService.class);
  private final OpenAIAsyncClient asyncClient;
  private final String deploymentName;
  private final SystemConfigurationService systemConfigurationService;
  private final ObjectMapper objectMapper;
  private final long requestTimeoutSeconds = 300; // 5 minutes timeout

  /**
   * Creates a new OpenAIService with the provided dependencies.
   *
   * @param asyncClient The pre-configured OpenAI async client
   * @param deploymentName The OpenAI deployment name to use
   * @param systemConfigurationService The system configuration service
   */
  public OpenAIService(
      OpenAIAsyncClient asyncClient,
      @Value("${app.openai.deployment-name}") String deploymentName,
      SystemConfigurationService systemConfigurationService) {
    this.asyncClient = asyncClient;
    this.deploymentName = deploymentName;
    this.systemConfigurationService = systemConfigurationService;
    this.objectMapper = new ObjectMapper();
  }

  @Override
  public AnalysisResult analyzeTender(String tenderInformation) {
    SystemConfiguration configuration = systemConfigurationService.get();
    String analysisPrompt = configuration.getAiAnalysisAnalysisPrompt();

    return analyzeTender(tenderInformation, analysisPrompt);
  }

  @Override
  public AnalysisResult analyzeTender(String tenderInformation, String customPrompt) {
    AnalysisResult analysisResult = new AnalysisResult();

    SystemConfiguration configuration = systemConfigurationService.get();
    String systemPrompt = configuration.getAiAnalysisSystemPrompt();

    List<ChatRequestMessage> chatMessages = new ArrayList<>();
    chatMessages.add(new ChatRequestSystemMessage(systemPrompt));
    chatMessages.add(new ChatRequestUserMessage(customPrompt));
    chatMessages.add(new ChatRequestUserMessage(tenderInformation));
    // logger.debug("OpenAiService - System Prompt: {}", systemPrompt);
    // logger.debug("OpenAiService - Custom Prompt: {}", customPrompt);
    // logger.debug("OpenAiService - Tender Information: {}", tenderInformation);

    ChatCompletionsOptions chatCompletionsOptions = new ChatCompletionsOptions(chatMessages);
    chatCompletionsOptions.setN(1);

    logger.info("OpenAiService - Sending request to OpenAI service.");

    CompletableFuture<ChatCompletions> future =
        asyncClient.getChatCompletions(deploymentName, chatCompletionsOptions).toFuture();

    try {
      ChatCompletions chatCompletions = future.get(requestTimeoutSeconds, TimeUnit.SECONDS);

      logger.info(
          "OpenAiService - Received response from OpenAI service [PromptTokens: {}, CompletionTokens: {}]",
          chatCompletions.getUsage().getPromptTokens(),
          chatCompletions.getUsage().getCompletionTokens());

      List<ChatChoice> choices = chatCompletions.getChoices();
      if (choices == null || choices.isEmpty()) {
        String message = "OpenAiService - Result from OpenAI Service does not contain any choices";
        logger.debug(message);
        throw new RuntimeException(message);
      }

      ChatChoice choice = choices.getFirst();
      ChatResponseMessage message = choice.getMessage();
      String generatedOutput = message.getContent();
      logger.debug("OpenAiService - analyzeTender model output: {}", generatedOutput);

      analysisResult.setAnalysisResult(generatedOutput);
      analysisResult.setPromptTokens(chatCompletions.getUsage().getPromptTokens());
      analysisResult.setCompletionTokens(chatCompletions.getUsage().getCompletionTokens());
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      analysisResult.setAnalysisResult("Interrupted: " + e.getMessage());
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    } catch (ExecutionException e) {
      analysisResult.setAnalysisResult("Execution error: " + e.getCause().getMessage());
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    } catch (TimeoutException e) {
      future.cancel(true);
      analysisResult.setAnalysisResult("Timeout after " + requestTimeoutSeconds + "s");
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    } catch (Exception e) {
      analysisResult.setAnalysisResult("Unexpected error: " + e.getMessage());
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    }
    return analysisResult;
  }

  @Override
  public TenderExtractionResult extractTenderFields(String tenderInformation) {
    SystemConfiguration configuration = systemConfigurationService.get();
    String extractionPrompt = configuration.getAiStructuredOutputPrompt();

    List<ChatRequestMessage> chatMessages = new ArrayList<>();
    chatMessages.add(
        new ChatRequestSystemMessage(
            "You are an AI assistant that extracts structured tender information from text. Always respond with valid JSON only."));
    chatMessages.add(new ChatRequestUserMessage(extractionPrompt));
    chatMessages.add(new ChatRequestUserMessage(tenderInformation));

    ChatCompletionsOptions chatCompletionsOptions = new ChatCompletionsOptions(chatMessages);
    chatCompletionsOptions.setResponseFormat(new ChatCompletionsJsonResponseFormat());
    chatCompletionsOptions.setN(1);
    chatCompletionsOptions.setTemperature(0.1); // Low temperature for more consistent extraction

    logger.debug(
        "OpenAiService - Sending extractTenderFields request to OpenAI service [Prompts: {}]",
        chatMessages);

    CompletableFuture<ChatCompletions> future =
        asyncClient.getChatCompletions(deploymentName, chatCompletionsOptions).toFuture();

    try {
      ChatCompletions chatCompletions = future.get(requestTimeoutSeconds, TimeUnit.SECONDS);

      logger.debug(
          "OpenAiService - Received extraction response from OpenAI service [PromptTokens: {}, CompletionTokens: {}]",
          chatCompletions.getUsage().getPromptTokens(),
          chatCompletions.getUsage().getCompletionTokens());

      List<ChatChoice> choices = chatCompletions.getChoices();
      if (choices == null || choices.isEmpty()) {
        String message = "Result from OpenAI Service does not contain any choices";
        logger.error(message);
        throw new RuntimeException(message);
      }

      ChatChoice choice = choices.getFirst();
      ChatResponseMessage message = choice.getMessage();
      String generatedOutput = message.getContent();
      logger.debug("OpenAiService - Extraction model output: {}", generatedOutput);

      // parse the JSON response
      try {
        JsonNode jsonNode = objectMapper.readTree(generatedOutput);
        TenderExtractionResult result =
            objectMapper.treeToValue(jsonNode, TenderExtractionResult.class);
        result.setAiRawMessage(generatedOutput);
        logger.info("Successfully extracted tender fields");
        return result;
      } catch (Exception e) {
        logger.error("Failed to parse OpenAI response as JSON: {}", generatedOutput, e);
        return null;
      }

    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      logger.error("OpenAI extraction request was interrupted", e);
      return null;
    } catch (ExecutionException e) {
      logger.error("Error executing OpenAI extraction request", e.getCause());
      return null;
    } catch (TimeoutException e) {
      future.cancel(true);
      logger.error(
          "OpenAI extraction request timed out after {} seconds", requestTimeoutSeconds, e);
      return null;
    }
  }

  @Override
  public TenderExtractionResult combineTenderFields(
      List<TenderExtractionResult> tenderExtractionResults) {
    SystemConfiguration configuration = systemConfigurationService.get();
    String combineFieldsPrompt = configuration.getAiCombineExtractedTenderFieldsPrompt();

    List<ChatRequestMessage> chatMessages = new ArrayList<>();
    chatMessages.add(
        new ChatRequestSystemMessage(
            "You are an AI assistant that extracts structured tender information from text. Always respond with valid JSON only."));
    chatMessages.add(new ChatRequestUserMessage(combineFieldsPrompt));

    for (TenderExtractionResult extractionResult : tenderExtractionResults) {
      chatMessages.add(new ChatRequestUserMessage(extractionResult.getAiRawMessage()));
    }

    ChatCompletionsOptions chatCompletionsOptions = new ChatCompletionsOptions(chatMessages);
    chatCompletionsOptions.setResponseFormat(new ChatCompletionsJsonResponseFormat());
    chatCompletionsOptions.setN(1);
    chatCompletionsOptions.setTemperature(0.1); // Low temperature for more consistent extraction

    logger.debug(
        "OpenAiService - Sending combineTenderFields request to OpenAI service [Prompts: {}]",
        chatMessages);

    CompletableFuture<ChatCompletions> future =
        asyncClient.getChatCompletions(deploymentName, chatCompletionsOptions).toFuture();

    try {
      ChatCompletions chatCompletions = future.get(requestTimeoutSeconds, TimeUnit.SECONDS);

      logger.debug(
          "OpenAiService - Received extraction response from OpenAI service [PromptTokens: {}, CompletionTokens: {}]",
          chatCompletions.getUsage().getPromptTokens(),
          chatCompletions.getUsage().getCompletionTokens());

      List<ChatChoice> choices = chatCompletions.getChoices();
      if (choices == null || choices.isEmpty()) {
        String message = "Result from OpenAI Service does not contain any choices";
        logger.error(message);
        throw new RuntimeException(message);
      }

      ChatChoice choice = choices.getFirst();
      ChatResponseMessage message = choice.getMessage();
      String generatedOutput = message.getContent();
      logger.debug("OpenAiService - Combine extracted fields model output: {}", generatedOutput);

      // parse the JSON response
      try {
        JsonNode jsonNode = objectMapper.readTree(generatedOutput);
        TenderExtractionResult result =
            objectMapper.treeToValue(jsonNode, TenderExtractionResult.class);
        result.setAiRawMessage(generatedOutput);
        logger.info("Successfully extracted tender fields");
        return result;
      } catch (Exception e) {
        logger.error("Failed to parse OpenAI response as JSON: {}", generatedOutput, e);
        return null;
      }

    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      logger.error("OpenAI extraction request was interrupted", e);
      return null;
    } catch (ExecutionException e) {
      logger.error("Error executing OpenAI extraction request", e.getCause());
      return null;
    } catch (TimeoutException e) {
      future.cancel(true);
      logger.error(
          "OpenAI extraction request timed out after {} seconds", requestTimeoutSeconds, e);
      return null;
    }
  }

  @Override
  public com.gofore.aita.analysis.models.AnalysisResult combineAnalysisResults(
      List<com.gofore.aita.analysis.models.AnalysisResult> analysisResults) {
    AnalysisResult analysisResult = new AnalysisResult();

    SystemConfiguration configuration = systemConfigurationService.get();
    String systemPrompt = configuration.getAiAnalysisSystemPrompt();
    String analysisPrompt = configuration.getAiAnalysisAnalysisPrompt();
    String combineAnalysisPrompt = configuration.getAiCombineAnalysisTenderFieldsPrompt();

    List<ChatRequestMessage> chatMessages = new ArrayList<>();
    chatMessages.add(new ChatRequestSystemMessage(systemPrompt));
    chatMessages.add(new ChatRequestUserMessage(combineAnalysisPrompt));
    chatMessages.add(new ChatRequestUserMessage(analysisPrompt));
    for (com.gofore.aita.analysis.models.AnalysisResult result : analysisResults) {
      chatMessages.add(new ChatRequestUserMessage(result.getAnalysisResult()));
    }

    ChatCompletionsOptions chatCompletionsOptions = new ChatCompletionsOptions(chatMessages);
    chatCompletionsOptions.setN(1);
    chatCompletionsOptions.setTemperature(0.1); // Low temperature for more consistent extraction

    logger.debug("OpenAiService combineAnalysisResults - Sending request to OpenAI service.");

    CompletableFuture<ChatCompletions> future =
        asyncClient.getChatCompletions(deploymentName, chatCompletionsOptions).toFuture();

    try {
      ChatCompletions chatCompletions = future.get(requestTimeoutSeconds, TimeUnit.SECONDS);

      logger.debug(
          "OpenAiService - Received response from OpenAI service [PromptTokens: {}, CompletionTokens: {}]",
          chatCompletions.getUsage().getPromptTokens(),
          chatCompletions.getUsage().getCompletionTokens());

      List<ChatChoice> choices = chatCompletions.getChoices();
      if (choices == null || choices.isEmpty()) {
        String message = "OpenAiService - Result from OpenAI Service does not contain any choices";
        logger.debug(message);
        throw new RuntimeException(message);
      }

      ChatChoice choice = choices.getFirst();
      ChatResponseMessage message = choice.getMessage();
      String generatedOutput = message.getContent();
      logger.debug("OpenAiService - combineAnalysisResults model output: {}", generatedOutput);

      analysisResult.setAnalysisResult(generatedOutput);
      analysisResult.setPromptTokens(chatCompletions.getUsage().getPromptTokens());
      analysisResult.setCompletionTokens(chatCompletions.getUsage().getCompletionTokens());
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      analysisResult.setAnalysisResult("Interrupted: " + e.getMessage());
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    } catch (ExecutionException e) {
      analysisResult.setAnalysisResult("Execution error: " + e.getCause().getMessage());
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    } catch (TimeoutException e) {
      future.cancel(true);
      analysisResult.setAnalysisResult("Timeout after " + requestTimeoutSeconds + "s");
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    } catch (Exception e) {
      analysisResult.setAnalysisResult("Unexpected error: " + e.getMessage());
      analysisResult.setPromptTokens(0);
      analysisResult.setCompletionTokens(0);
    }
    return analysisResult;
  }
}
