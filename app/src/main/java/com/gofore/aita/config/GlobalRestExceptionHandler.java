package com.gofore.aita.config;

import static org.springframework.http.HttpStatus.*;

import com.gofore.aita.tender.api.dto.ErrorDTO;
import com.gofore.aita.tender.domain.exceptions.AITenderCreationExtractionException;
import com.gofore.aita.tender.domain.exceptions.BadRequestException;
import com.gofore.aita.tender.domain.exceptions.ResourceNotFoundException;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/** Global handler for all exceptions that occur during incoming HTTP requests. */
@ControllerAdvice
public class GlobalRestExceptionHandler {

  private final Logger logger = LoggerFactory.getLogger(GlobalRestExceptionHandler.class);

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorDTO> handleException(Exception ex) {
    logger.warn("An unknown error occurred: {}", ex.getMessage(), ex);
    ErrorDTO error =
        new ErrorDTO("An error occurred while processing the request", List.of(ex.getMessage()));
    return new ResponseEntity<>(error, INTERNAL_SERVER_ERROR);
  }

  @ExceptionHandler(HttpMessageNotReadableException.class)
  public ResponseEntity<ErrorDTO> handleException(HttpMessageNotReadableException ex) {
    ErrorDTO error =
        new ErrorDTO("An error occurred while parsing the request", List.of(ex.getMessage()));
    return new ResponseEntity<>(error, BAD_REQUEST);
  }

  @ExceptionHandler(BadRequestException.class)
  public ResponseEntity<ErrorDTO> handleException(BadRequestException ex) {
    ErrorDTO error =
        new ErrorDTO("An error occurred while processing the request", List.of(ex.getMessage()));
    return new ResponseEntity<>(error, BAD_REQUEST);
  }

  @ExceptionHandler(AITenderCreationExtractionException.class)
  public ResponseEntity<ErrorDTO> handleException(AITenderCreationExtractionException ex) {
    logger.info("AI extraction failed for tender creation: {}", ex.getMessage());
    ErrorDTO error = new ErrorDTO("AI‐assisted tender creation failed", List.of(ex.getMessage()));

    return new ResponseEntity<>(error, UNPROCESSABLE_ENTITY);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<ErrorDTO> handleException(MethodArgumentNotValidException ex) {
    List<FieldError> fields = ex.getFieldErrors();
    List<String> validationErrors = new ArrayList<>(fields.size());
    for (FieldError error : fields) {
      String fieldError = "%s: %s".formatted(error.getField(), error.getDefaultMessage());
      validationErrors.add(fieldError);
    }
    ErrorDTO error = new ErrorDTO("Field validation failed", validationErrors);
    return new ResponseEntity<>(error, BAD_REQUEST);
  }

  @ExceptionHandler(ResourceNotFoundException.class)
  public ResponseEntity<Void> handleException(ResourceNotFoundException ex) {
    return new ResponseEntity<>(HttpStatus.NOT_FOUND);
  }
}
