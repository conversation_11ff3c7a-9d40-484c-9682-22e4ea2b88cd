package com.gofore.aita.config;

import com.azure.ai.openai.OpenAIAsyncClient;
import com.azure.ai.openai.OpenAIClient;
import com.azure.ai.openai.OpenAIClientBuilder;
import com.azure.core.credential.TokenCredential;
import com.azure.core.http.HttpClient;
import com.azure.core.http.okhttp.OkHttpAsyncHttpClientBuilder;
import com.azure.core.http.policy.*;
import com.azure.identity.AzureCliCredentialBuilder;
import com.azure.identity.ManagedIdentityCredentialBuilder;
import jakarta.annotation.PreDestroy;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;

/**
 * Configuration for Azure OpenAI client. This class provides a centralized configuration for the
 * Azure OpenAI client, including OkHttp client configuration to avoid Netty channel registration
 * issues.
 */
@Configuration
public class AzureOpenAIConfig {

  private final Logger logger = LoggerFactory.getLogger(AzureOpenAIConfig.class);
  private final Environment environment;
  private ConnectionPool connectionPool;
  private OkHttpClient okHttpClient;

  FixedDelayOptions delayOptions = new FixedDelayOptions(5, Duration.ofSeconds(5));
  RetryOptions retryOptions = new RetryOptions(delayOptions);
  RetryPolicy retryPolicy = new RetryPolicy(retryOptions);
  HttpLogOptions logOptions = new HttpLogOptions().setLogLevel(HttpLogDetailLevel.BASIC);

  public AzureOpenAIConfig(Environment environment) {
    this.environment = environment;
  }

  /**
   * Creates an OkHttp ConnectionPool for connection reuse. The pool is sized based on the number of
   * available processors.
   *
   * @return The configured ConnectionPool
   */
  @Bean
  public ConnectionPool openAiConnectionPool() {
    int maxIdleConnections = Runtime.getRuntime().availableProcessors() * 5;
    long keepAliveDuration = 5;
    logger.info(
        "Creating OkHttp ConnectionPool with {} max idle connections and {} minutes keep-alive",
        maxIdleConnections,
        keepAliveDuration);
    connectionPool = new ConnectionPool(maxIdleConnections, keepAliveDuration, TimeUnit.MINUTES);
    return connectionPool;
  }

  /**
   * Creates an OkHttp client with appropriate timeouts and connection pooling. This client is used
   * by the Azure SDK HTTP client.
   *
   * @param connectionPool The shared connection pool
   * @return The configured OkHttpClient
   */
  @Bean
  public OkHttpClient openAiOkHttpClient(ConnectionPool connectionPool) {
    okHttpClient =
        new OkHttpClient.Builder()
            .connectionPool(connectionPool)
            .retryOnConnectionFailure(true)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(8, TimeUnit.MINUTES) // for longer OpenAI responses
            .writeTimeout(8, TimeUnit.MINUTES)
            .callTimeout(10, TimeUnit.MINUTES) // overall timeout for the entire request
            .build();
    return okHttpClient;
  }

  /**
   * Creates an HTTP client that uses the shared OkHttpClient. This client is configured with
   * appropriate timeouts for OpenAI API calls.
   *
   * @param okHttpClient The shared OkHttpClient
   * @return The configured HttpClient
   */
  @Bean
  public HttpClient openAiHttpClient(OkHttpClient okHttpClient) {
    return new OkHttpAsyncHttpClientBuilder(okHttpClient)
        .readTimeout(java.time.Duration.ofMinutes(5)) // set read timeout at Azure SDK level
        .build();
  }

  /**
   * Creates the OpenAI async client with the shared HTTP client and appropriate credentials. This
   * is the preferred client to avoid Netty channel registration issues with the sync client.
   *
   * @param httpClient The shared HTTP client
   * @param openAiEndpoint The OpenAI API endpoint
   * @param managedIdentityClientId The managed identity client ID
   * @return The configured OpenAIAsyncClient
   */
  @Bean
  @Primary
  public OpenAIAsyncClient openAIAsyncClient(
      HttpClient httpClient,
      @Value("${app.openai.endpoint}") String openAiEndpoint,
      @Value("${app.identity.managed-identity.client-id}") String managedIdentityClientId) {

    return new OpenAIClientBuilder()
        .credential(generateCredential(managedIdentityClientId))
        .endpoint(openAiEndpoint)
        .httpClient(httpClient)
        .retryPolicy(retryPolicy)
        .httpLogOptions(logOptions)
        .buildAsyncClient();
  }

  /**
   * Creates the OpenAI synchronous client with the shared HTTP client and appropriate credentials.
   * Note: This client may encounter Netty channel registration issues on HTTP error paths. Prefer
   * using the async client when possible.
   *
   * @param httpClient The shared HTTP client
   * @param openAiEndpoint The OpenAI API endpoint
   * @param managedIdentityClientId The managed identity client ID
   * @return The configured OpenAIClient
   */
  @Bean
  public OpenAIClient openAIClient(
      HttpClient httpClient,
      @Value("${app.openai.endpoint}") String openAiEndpoint,
      @Value("${app.identity.managed-identity.client-id}") String managedIdentityClientId) {

    return new OpenAIClientBuilder()
        .credential(generateCredential(managedIdentityClientId))
        .endpoint(openAiEndpoint)
        .httpClient(httpClient)
        .retryPolicy(retryPolicy)
        .httpLogOptions(logOptions)
        .buildClient();
  }

  /**
   * Generate credential which is required to perform requests against Azure services. In the Cloud
   * environment a Managed Identity is used to generate credentials, whereas in the local
   * environment the Azure CLI can be used to generate credentials. The method automatically
   * evaluates which option to use.
   *
   * @param managedIdentityClientId Client ID of the managed identity to be used
   * @return A Credential that is being used for authentication with Azure services
   */
  private TokenCredential generateCredential(String managedIdentityClientId) {
    for (String profile : environment.getActiveProfiles()) {
      if (profile.equals("dev")) {
        return new AzureCliCredentialBuilder().build();
      }
    }
    return new ManagedIdentityCredentialBuilder().clientId(managedIdentityClientId).build();
  }

  /**
   * Gracefully shuts down the OkHttpClient when the Spring context is closed. This ensures proper
   * resource cleanup.
   */
  @PreDestroy
  public void shutdown() {
    if (connectionPool != null) {
      logger.info("Shutting down OkHttp ConnectionPool");
      connectionPool.evictAll();
    }
    if (okHttpClient != null) {
      logger.info("Shutting down OkHttpClient");
      okHttpClient.dispatcher().executorService().shutdown();
    }
  }
}
