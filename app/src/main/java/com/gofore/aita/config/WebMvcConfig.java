package com.gofore.aita.config;

import com.gofore.aita.tender.api.converter.CollectionSortDirectionConverter;
import com.gofore.aita.tender.api.converter.TenderSortableFieldsConverter;
import com.gofore.aita.tender.api.converter.WorkflowStatusConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

  @Value("${app.cors.origins}")
  private String corsAllowedOrigins;

  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry
        .addMapping("/**")
        .allowedOrigins(corsAllowedOrigins)
        .allowedMethods("*")
        .allowedHeaders("*");
  }

  @Override
  public void addFormatters(FormatterRegistry registry) {
    registry.addConverter(new TenderSortableFieldsConverter());
    registry.addConverter(new CollectionSortDirectionConverter());
    registry.addConverter(new WorkflowStatusConverter());
  }
}
