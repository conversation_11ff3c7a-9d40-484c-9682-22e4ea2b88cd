package com.gofore.aita.core.api;

import com.gofore.aita.core.api.dto.SystemConfigurationDTO;
import com.gofore.aita.core.api.mapper.SystemConfigurationMapper;
import com.gofore.aita.core.domain.SystemConfigurationService;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/config")
public class SystemConfigurationController {

  private final SystemConfigurationService systemConfigurationService;
  private final SystemConfigurationMapper systemConfigurationMapper;

  public SystemConfigurationController(
      SystemConfigurationService systemConfigurationService,
      SystemConfigurationMapper systemConfigurationMapper) {
    this.systemConfigurationService = systemConfigurationService;
    this.systemConfigurationMapper = systemConfigurationMapper;
  }

  @GetMapping
  public ResponseEntity<SystemConfigurationDTO> getSystemConfiguration() {
    SystemConfiguration systemConfiguration = systemConfigurationService.get();
    return new ResponseEntity<>(systemConfigurationMapper.map(systemConfiguration), HttpStatus.OK);
  }

  @PutMapping
  public ResponseEntity<SystemConfigurationDTO> updateSystemConfiguration(
      @RequestBody SystemConfigurationDTO systemConfigurationDTO) {
    SystemConfiguration systemConfiguration = systemConfigurationMapper.map(systemConfigurationDTO);
    SystemConfiguration updatedSystemConfiguration =
        systemConfigurationService.update(systemConfiguration);
    return new ResponseEntity<>(
        systemConfigurationMapper.map(updatedSystemConfiguration), HttpStatus.OK);
  }
}
