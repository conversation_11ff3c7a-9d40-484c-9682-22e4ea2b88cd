package com.gofore.aita.core.domain.mapper;

import com.gofore.aita.core.domain.models.SystemConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface SystemConfigurationUpdateMapper {

  @Mapping(target = "id", ignore = true)
  void updatedConfiguration(
      SystemConfiguration updatedConfiguration,
      @MappingTarget SystemConfiguration existingConfiguration);
}
