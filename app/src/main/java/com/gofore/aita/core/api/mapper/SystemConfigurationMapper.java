package com.gofore.aita.core.api.mapper;

import com.gofore.aita.core.api.dto.SystemConfigurationDTO;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface SystemConfigurationMapper {
  @Mapping(target = "id", ignore = true)
  SystemConfiguration map(SystemConfigurationDTO source);

  SystemConfigurationDTO map(SystemConfiguration source);
}
