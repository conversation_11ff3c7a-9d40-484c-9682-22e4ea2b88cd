package com.gofore.aita.core.domain.models;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "config")
public class SystemConfiguration {
  private static final String AI_ANALYSIS_SYSTEM_PROMPT =
      "You are an AI assistant " + "that supports us in analysing tenders.";
  private static final String AI_ANALYSIS_ANALYSIS_PROMPT =
      "You will receive a description and documents of the tender. "
          + "Please try to generate a short report highlighting the most salient points.";
  private static final String AI_STRUCTURED_OUTPUT_PROMPT =
      "You are a tender-extraction service. Read the following text and output **only** a single JSON object (no markdown, no code fences, no extra commentary) with exactly these fields and in this order:\n"
          + "  client (issuer of this tender offer): string | null,\n"
          + "  submissionDate (Exact date by which the entire delivery package must be delivered.): string | null,\n"
          + "  bindingDeadline (The binding deadline is the period during which a bidder is legally bound by their offer. Within this period, the offer cannot be modified or withdrawn.): string | null,\n"
          + "  contractDuration (How long the tender offer will last if contract awarded.): string | null,\n"
          + "  publicationDate: string | null,\n"
          + "  questionDeadline: string | null,\n"
          + "  contractValue: number | null,\n"
          + "  maximumBudget (The maximum amount listed in person-days (PT).): number | null,\n"
          + "  winningCriteria (List of criteria that are especially crucial for awarding the contract.): string | null,\n"
          + "  weightingPriceQuality (Weighting of price versus quality.): string | null,\n"
          + "  deliveryLocation: string | null\n\n"
          + "Field requirements:\n"
          + "- Dates must use ISO 8601 (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)\n"
          + "- Numeric fields (contractValue, maximumBudget) should be plain numbers (no currency symbols).\n"
          + "- If a field cannot be determined, set its value to null.";

  private static final String AI_COMBINE_EXTRACTION_STRUCTURED_OUTPUT_PROMPT =
      "You are a tender-extraction service. You have already extracted multiple times information from documents and returned JSON objects. Now accumulate the results in a single JSON object. If one JSON object contains a placeholder value, use the value from other JSON objects if available.\n"
          + "Output **only** one single JSON object (no markdown, no code fences, no extra commentary) with exactly these fields and in this order:\n"
          + "  client (issuer of this tender offer): string | PLATZHALTER_CLIENT,\n"
          + "  submissionDate (Exact date by which the entire delivery package must be delivered.): string | 0000-00-00T00:00:00,\n"
          + "  bindingDeadline (The binding deadline is the period during which a bidder is legally bound by their offer. Within this period, the offer cannot be modified or withdrawn.): string | 0000-00-00,\n"
          + "  contractDuration (How long the tender offer will last if contract awarded.): string | PLATZHALTER_CONTRACT_DURATION,\n"
          + "  publicationDate: string | 0000-00-00,\n"
          + "  questionDeadline: string | 0000-00-00T00:00:00,\n"
          + "  contractValue: number | -1,\n"
          + "  maximumBudget (The maximum amount listed in person-days (PT).): number | PLATZHALTER_MAXIMUM_BUDGET,\n"
          + "  winningCriteria (List of criteria that are especially crucial for awarding the contract.): string | PLATZHALTER_WINNING_CRITERIA,\n"
          + "  weightingPriceQuality (Weighting of price versus quality.): string | PLATZHALTER_WEIGHTING,\n"
          + "  deliveryLocation: string | PLATZHALTER_DELIVERY_LOCATION\n"
          + "  rating: number 1-5 (Assessment of the quality of the interest based on the following criteria: 1 is low rating, 5 is the best, relevant facts: Award quality --> the higher quality is rated compared to price, the better; remote work must be possible, unless the fulfillment locations are Vienna, Salzburg, Munich, Stuttgart, Braunschweig; if the use of modern technologies is important, this increases the rating; the larger the total amount and duration of the contract, the better)| -1\n\n"
          + "**Important**:\n"
          + "- Every field in the JSON responses contains either a real value or a placeholder value. **Only** use the placeholder value in the final output if you cannot find a real value in any of the JSON objects.\n"
          + "- You can see the placeholder values of the fields behind the '|' symbol.\n\n"
          + "Field requirements:\n"
          + "- Dates must use ISO 8601 (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)\n"
          + "- Numeric fields (contractValue, maximumBudget) should be plain numbers (no currency symbols).\n"
          + "- **No** fields should be null or missing.";

  private static final String AI_COMBINE_ANALYSIS_PROMPT =
      "You are a tender-analysis service. You have already analyzed multiple times information from documents and returned formatted text. Now **accumulate** all those previous results by combining all the information in a single text which has the same format (tables, markdown, etc.) as the previous analysis results.\n"
          + "**Important:**\n"
          + "- If one analysis contains placeholders (rows in the tables), use the values from the other analysis if available.\n"
          + "- **Don't lose** any information of the original analysis results (except for the placeholders)!\n"
          + "- The previous results contain tables. Preserve whole columns, **don't mix fields from different columns**! So if there is a table with non-placeholder values, add the whole column to the final results table.\n"
          + "- If you cannot find a value in one analysis result, use the value from the other analysis result if available.\n"
          + "- Just combine the given results and don't add any additional commentary.\n"
          + "- For duplicate information keep the information only once (even if it is in different language or phrased a little bit differently).\n"
          + "- The next message contains the prompt that has been used for the previous analysis so you can understand the context and the format which the combined result should have. Afterwards you will receive the analysis results which you have to combine.";

  @Id private String id;
  private String aiAnalysisSystemPrompt;
  private String aiAnalysisAnalysisPrompt;
  private String aiStructuredOutputPrompt;
  private String aiCombineExtractedTenderFieldsPrompt;
  private String aiCombineAnalysisTenderFieldsPrompt;

  public static SystemConfiguration defaultConfiguration() {
    SystemConfiguration systemConfiguration = new SystemConfiguration();
    systemConfiguration.setAiAnalysisSystemPrompt(AI_ANALYSIS_SYSTEM_PROMPT);
    systemConfiguration.setAiAnalysisAnalysisPrompt(AI_ANALYSIS_ANALYSIS_PROMPT);
    systemConfiguration.setAiStructuredOutputPrompt(AI_STRUCTURED_OUTPUT_PROMPT);
    systemConfiguration.setAiCombineExtractedTenderFieldsPrompt(
        AI_COMBINE_EXTRACTION_STRUCTURED_OUTPUT_PROMPT);
    systemConfiguration.setAiCombineAnalysisTenderFieldsPrompt(AI_COMBINE_ANALYSIS_PROMPT);
    return systemConfiguration;
  }
}
