package com.gofore.aita.dtad.model;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DTADTender {
  
  // Fields from first row (rfqRow)
  private String title;              // Title from span.listLink
  private String region;             // Region from td.list-column1 (e.g., "20459 Hamburg")
  private String datum;              // Date from td.list-column1 (e.g., "04.09.2025")
  private String frist;              // Deadline from td.list-column1 (e.g., "30.09.2025")
  
  // Fields from subsequent rows (key-value pairs)
  private String dtadId;             // DTAD-ID: (e.g., "23439956")
  private String auftragsart;        // Auftragsart: (e.g., "Nationale Ausschreibung")
  private String fristAngebotsabgabe; // Frist Angebotsabgabe: (e.g., "30.09.2025")
  private String dokumententyp;      // Dokumententyp: (e.g., "Ausschreibung")
  private String zeitvertragEndet;   // Zeitvertrag endet: (e.g., "Dezember 2026")
  private String beschreibung;       // Beschreibung: (full description)
  private String auftraggeber;       // Auftraggeber: or Vergabestelle: (contracting authority)
  private String erfuellungsort;     // Erfüllungsort: (place of performance)
  
  // Processing metadata
  private LocalDateTime extractedAt;     // When this data was extracted
  private String extractionSource;      // Source of extraction (e.g., "DTAD")
  private String profileId;             // Search profile ID used to find this tender
  
  /**
   * Get a display-friendly title with fallback
   */
  public String getDisplayTitle() {
    if (title != null && !title.trim().isEmpty()) {
      return title.trim();
    }
    if (beschreibung != null && !beschreibung.trim().isEmpty()) {
      // Return first line of description if title is empty
      String firstLine = beschreibung.split("\\\\<br>|<br>|\\n")[0];
      return firstLine.trim();
    }
    return "Tender " + (dtadId != null ? dtadId : "Unknown");
  }
  
  /**
   * Get a unique identifier for this tender
   */
  public String getUniqueId() {
    return dtadId != null ? dtadId : "unknown";
  }
  
  /**
   * Get location display string
   */
  public String getLocationDisplay() {
    if (region != null && !region.trim().isEmpty()) {
      return region.trim();
    }
    if (erfuellungsort != null && !erfuellungsort.trim().isEmpty()) {
      return erfuellungsort.trim();
    }
    return "";
  }
  
  /**
   * Create a summary string for logging/debugging
   */
  public String getSummary() {
    return String.format("DTADTender{id='%s', title='%s', region='%s', frist='%s'}", 
                        dtadId, 
                        getDisplayTitle().length() > 50 ? getDisplayTitle().substring(0, 47) + "..." : getDisplayTitle(),
                        region, 
                        frist);
  }
}
