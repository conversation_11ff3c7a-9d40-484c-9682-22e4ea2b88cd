package com.gofore.aita.dtad.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DTADTender {

  // Basic tender identification
  private String tenderId; // e.g., "23439956_1"
  private String tenderNumber; // e.g., "23439956"
  private String title; // Tender title
  private String shortDescription; // Brief description
  private String fullDescription; // Complete description if available

  // Tender classification
  private String tenderType; // e.g., "Nationale Ausschreibung"
  private String documentType; // e.g., "Ausschreibung"
  private String category; // Tender category
  private String sector; // Business sector

  // Location information
  private String location; // City/region
  private String country; // Country code or name
  private String postalCode; // Postal code if available

  // Dates and deadlines
  private LocalDate publicationDate; // When tender was published
  private LocalDate submissionDeadline; // Deadline for bid submission (Frist Angebotsabgabe)
  private LocalDate contractEndDate; // Contract end date (Zeitvertrag endet)
  private LocalDate lastUpdated; // Last update date

  // Organization information
  private String contractingAuthority; // Organization issuing the tender
  private String contactPerson; // Contact person if available
  private String contactEmail; // Contact email if available
  private String contactPhone; // Contact phone if available

  // Financial information
  private String estimatedValue; // Estimated contract value
  private String currency; // Currency
  private String budgetRange; // Budget range if specified

  // Technical details
  private String cpvCode; // Common Procurement Vocabulary code
  private String procedureType; // Procurement procedure type
  private String awardCriteria; // Award criteria

  // URLs and references
  private String detailUrl; // URL to detailed tender information
  private String sourceUrl; // Original source URL
  private String documentUrl; // URL to tender documents

  // Processing metadata
  private LocalDateTime extractedAt; // When this data was extracted
  private String extractionSource; // Source of extraction (e.g., "DTAD")
  private String profileId; // Search profile ID used to find this tender
  private String rawHtml; // Raw HTML snippet for debugging (optional)

  // Status and flags
  private TenderStatus status; // Current status
  private boolean isArchived; // Whether tender is archived
  private boolean hasDocuments; // Whether documents are available
  private boolean isFrameworkAgreement; // Whether this is a framework agreement

  // Additional metadata
  private String language; // Language of the tender
  private String legalBasis; // Legal basis for procurement
  private String notes; // Additional notes

  /** Enum for tender status */
  public enum TenderStatus {
    ACTIVE, // Tender is active and accepting bids
    EXPIRED, // Submission deadline has passed
    AWARDED, // Contract has been awarded
    CANCELLED, // Tender has been cancelled
    SUSPENDED, // Tender is temporarily suspended
    UNKNOWN // Status could not be determined
  }

  /** Get a display-friendly title with fallback */
  public String getDisplayTitle() {
    if (title != null && !title.trim().isEmpty()) {
      return title.trim();
    }
    if (shortDescription != null && !shortDescription.trim().isEmpty()) {
      return shortDescription.trim();
    }
    return "Tender " + (tenderNumber != null ? tenderNumber : tenderId);
  }

  /** Get a unique identifier for this tender */
  public String getUniqueId() {
    return tenderId != null ? tenderId : tenderNumber;
  }

  /** Check if this tender is still active (submission deadline not passed) */
  public boolean isActive() {
    if (status == TenderStatus.ACTIVE) {
      return true;
    }
    if (submissionDeadline != null) {
      return !submissionDeadline.isBefore(LocalDate.now());
    }
    return status != TenderStatus.EXPIRED
        && status != TenderStatus.AWARDED
        && status != TenderStatus.CANCELLED;
  }

  /** Get location display string */
  public String getLocationDisplay() {
    StringBuilder location = new StringBuilder();
    if (this.location != null && !this.location.trim().isEmpty()) {
      location.append(this.location.trim());
    }
    if (postalCode != null && !postalCode.trim().isEmpty()) {
      if (location.length() > 0) {
        location.append(" (").append(postalCode.trim()).append(")");
      } else {
        location.append(postalCode.trim());
      }
    }
    if (country != null
        && !country.trim().isEmpty()
        && !country.equalsIgnoreCase("DE")
        && !country.equalsIgnoreCase("Deutschland")) {
      if (location.length() > 0) {
        location.append(", ");
      }
      location.append(country.trim());
    }
    return location.toString();
  }

  /** Create a summary string for logging/debugging */
  public String getSummary() {
    return String.format(
        "DTADTender{id='%s', title='%s', type='%s', location='%s', deadline=%s}",
        getUniqueId(),
        getDisplayTitle().length() > 50
            ? getDisplayTitle().substring(0, 47) + "..."
            : getDisplayTitle(),
        tenderType,
        getLocationDisplay(),
        submissionDeadline);
  }
}
