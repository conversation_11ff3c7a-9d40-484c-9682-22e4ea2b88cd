package com.gofore.aita.dtad.service;

import java.io.IOException;
import java.time.Instant;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.java.net.cookiejar.JavaNetCookieJar;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DTADAuthService {

  @Value("${app.dtad.username}")
  private String username;

  @Value("${app.dtad.password}")
  private String password;

  private static final String LOGIN_URL = "https://www.dtad.de/workxl/myAccount/login/validate.do";
  private static final String KEEP_ALIVE_URL =
      "https://www.dtad.de/workxl/360/checkSessionAlive.do";
  private static final String USER_AGENT =
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36";

  private OkHttpClient httpClient;

  @Getter private SessionInfo currentSession;

  public DTADAuthService() {
    // Configure OkHttp client with automatic cookie handling
    this.httpClient =
        new OkHttpClient.Builder()
            .followRedirects(true)
            .followSslRedirects(true)
            .cookieJar(new JavaNetCookieJar(new java.net.CookieManager()))
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();
  }

  /**
   * Authenticates with DTAD using form-based login
   *
   * @return true if authentication was successful
   */
  public boolean authenticate() throws Exception {
    log.info("Starting authentication process with DTAD");

    if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
      throw new RuntimeException(
          "Username and password must be provided via environment variables");
    }

    log.debug("Using username for authentication: {}", username);

    // Create a fresh client for authentication to avoid cookie issues
    OkHttpClient freshClient =
        new OkHttpClient.Builder()
            .followRedirects(true)
            .followSslRedirects(true)
            .cookieJar(new JavaNetCookieJar(new java.net.CookieManager()))
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    // Use fresh client for this authentication session
    OkHttpClient originalClient = this.httpClient;
    this.httpClient = freshClient;

    try {
      // Submit login form
      boolean loginSuccess = submitLoginForm();

      if (loginSuccess) {
        // Store session information
        this.currentSession = new SessionInfo(Instant.now());
        log.info("Successfully authenticated with DTAD");
        return true;
      } else {
        log.error("Authentication failed");
        return false;
      }
    } catch (Exception e) {
      log.error("Authentication failed: {}", e.getMessage(), e);
      throw e;
    } finally {
      // Restore the original client
      this.httpClient = originalClient;
    }
  }

  /** Get a valid session, authenticating if necessary */
  public boolean getValidSession() throws Exception {
    // Check if we have a valid session
    if (currentSession != null && !currentSession.isExpired()) {
      // Try to validate session with keep-alive
      if (performKeepAlive()) {
        log.debug("Using existing valid session");
        return true;
      }
    }

    // Session expired or invalid, authenticate again
    log.info("Session expired or not available, authenticating again");
    return authenticate();
  }

  /** Perform session keep-alive to maintain active session */
  public boolean performKeepAlive() {
    try {
      log.debug("Performing session keep-alive");

      Request request =
          new Request.Builder()
              .url(KEEP_ALIVE_URL)
              .post(
                  RequestBody.create(
                      "", MediaType.parse("application/x-www-form-urlencoded; charset=utf-8")))
              .header("User-Agent", USER_AGENT)
              .header("Accept", "*/*")
              .header("Accept-Language", "de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7")
              .header("Origin", "https://www.dtad.de")
              .header("Sec-Fetch-Dest", "empty")
              .header("Sec-Fetch-Mode", "cors")
              .header("Sec-Fetch-Site", "same-origin")
              .build();

      try (Response response = httpClient.newCall(request).execute()) {
        if (response.isSuccessful()) {
          log.debug("Session keep-alive successful");
          if (currentSession != null) {
            currentSession.updateLastActivity();
          }
          return true;
        } else {
          log.warn("Session keep-alive failed with status: {}", response.code());
          return false;
        }
      }
    } catch (IOException e) {
      log.error("Session keep-alive failed: {}", e.getMessage(), e);
      return false;
    }
  }

  /** Submit login form to DTAD */
  private boolean submitLoginForm() throws IOException {
    log.debug("Logging in as {}", username);

    // Build form data for DTAD login
    FormBody formBody =
        new FormBody.Builder()
            .add("use360View", "1")
            .add("username", username)
            .add("password", password)
            .add("saveLogin", "1")
            .build();

    // Create login request
    Request request =
        new Request.Builder()
            .url(LOGIN_URL)
            .post(formBody)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .header("User-Agent", USER_AGENT)
            .header(
                "Accept",
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
            .header("Accept-Language", "de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7")
            .header("Cache-Control", "max-age=0")
            .header("Origin", "https://www.dtad.com")
            .header("Referer", "https://www.dtad.com/de/")
            .header("Sec-Fetch-Dest", "document")
            .header("Sec-Fetch-Mode", "navigate")
            .header("Sec-Fetch-Site", "cross-site")
            .header("Sec-Fetch-User", "?1")
            .header("Upgrade-Insecure-Requests", "1")
            .build();

    try (Response response = httpClient.newCall(request).execute()) {
      String finalUrl = response.request().url().toString();
      log.debug("Final URL after login redirects: {}", finalUrl);

      // Check if login was successful by examining the response
      if (response.isSuccessful()) {
        String responseBody = response.body().string();

        // Check for successful login indicators
        // DTAD typically redirects to the main dashboard after successful login
        if (finalUrl.contains("workxl") && !finalUrl.contains("login")) {
          log.info("Login successful - redirected to dashboard");
          return true;
        }

        // Check response body for error indicators
        if (responseBody.contains("error")
            || responseBody.contains("invalid")
            || responseBody.contains("Fehler")
            || responseBody.contains("ungültig")) {
          log.error("Login failed - error found in response");
          return false;
        }

        // If we got a successful response and no error indicators, assume success
        log.info("Login appears successful");
        return true;
      } else {
        log.error("Login failed with HTTP status: {}", response.code());
        return false;
      }
    }
  }

  /** Get the HTTP client with current session cookies */
  public OkHttpClient getAuthenticatedClient() throws Exception {
    if (!getValidSession()) {
      throw new RuntimeException("Failed to obtain valid DTAD session");
    }
    return httpClient;
  }

  /** Class to track session information and expiration */
  private static class SessionInfo {
    private final Instant createdAt;
    private Instant lastActivity;
    private static final long SESSION_TIMEOUT_MINUTES = 30; // DTAD session timeout

    public SessionInfo(Instant createdAt) {
      this.createdAt = createdAt;
      this.lastActivity = createdAt;
    }

    public void updateLastActivity() {
      this.lastActivity = Instant.now();
    }

    public boolean isExpired() {
      return Instant.now().isAfter(lastActivity.plusSeconds(SESSION_TIMEOUT_MINUTES * 60));
    }
  }
}
