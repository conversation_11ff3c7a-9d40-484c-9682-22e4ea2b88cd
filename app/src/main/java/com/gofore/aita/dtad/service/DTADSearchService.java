package com.gofore.aita.dtad.service;

import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DTADSearchService {

  @Autowired private DTADAuthService authService;

  private static final String PROFILE_EXECUTE_URL =
      "https://www.dtad.de/workxl/myAccount/profile/execute.do";
  private static final String USER_AGENT =
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36";

  /**
   * Execute a search profile and return the HTML response containing tender data
   *
   * @param profileId The ID of the search profile to execute
   * @return HTML response containing tender data
   */
  public String executeSearchProfile(String profileId) throws Exception {
    log.info("Executing DTAD search profile: {}", profileId);

    // Get authenticated HTTP client
    OkHttpClient client = authService.getAuthenticatedClient();

    // Build form data for profile execution
    FormBody formBody = new FormBody.Builder().add("id", profileId).add("360inline", "1").build();

    // Create request
    Request request =
        new Request.Builder()
            .url(PROFILE_EXECUTE_URL)
            .post(formBody)
            .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
            .header("User-Agent", USER_AGENT)
            .header("Accept", "*/*")
            .header("Accept-Language", "de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7")
            .header("Origin", "https://www.dtad.de")
            .header("Sec-Fetch-Dest", "empty")
            .header("Sec-Fetch-Mode", "cors")
            .header("Sec-Fetch-Site", "same-origin")
            .build();

    try (Response response = client.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        throw new IOException(
            "Failed to execute search profile: " + response.code() + " - " + response.message());
      }

      String htmlResponse = response.body().string();
      log.info(
          "Successfully executed search profile, received {} characters of HTML",
          htmlResponse.length());

      return htmlResponse;
    }
  }

  /**
   * Execute a search profile with retry logic
   *
   * @param profileId The ID of the search profile to execute
   * @param maxRetries Maximum number of retry attempts
   * @return HTML response containing tender data
   */
  public String executeSearchProfileWithRetry(String profileId, int maxRetries) throws Exception {
    Exception lastException = null;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        log.debug("Search profile execution attempt {} of {}", attempt, maxRetries);
        return executeSearchProfile(profileId);
      } catch (Exception e) {
        lastException = e;
        log.warn("Search profile execution attempt {} failed: {}", attempt, e.getMessage());

        if (attempt < maxRetries) {
          // If authentication failed, try to re-authenticate
          if (e.getMessage().contains("401")
              || e.getMessage().contains("403")
              || e.getMessage().contains("unauthorized")
              || e.getMessage().contains("forbidden")) {
            log.info("Authentication may have expired, attempting re-authentication");
            authService.authenticate();
          }

          // Wait before retry
          Thread.sleep(1000 * attempt);
        }
      }
    }

    throw new RuntimeException(
        "Failed to execute search profile after " + maxRetries + " attempts", lastException);
  }



  /**
   * Validate that the HTML response contains tender data
   *
   * @param htmlResponse The HTML response to validate
   * @return true if the response appears to contain tender data
   */
  public boolean validateSearchResponse(String htmlResponse) {
    if (htmlResponse == null || htmlResponse.trim().isEmpty()) {
      log.warn("HTML response is null or empty");
      return false;
    }

    // Check for key indicators that this is a valid tender search response
    boolean hasRfqRows = htmlResponse.contains("class=\"rfqRow\"");
    boolean hasListLinks = htmlResponse.contains("class=\"listLink\"");
    boolean hasProfileName = htmlResponse.contains("profileName");
    boolean hasContentContainer = htmlResponse.contains("content-container-listing");

    if (hasRfqRows && hasListLinks) {
      log.debug("HTML response validation successful - contains tender data");
      return true;
    } else {
      log.warn("HTML response validation failed - missing expected tender data indicators");
      log.debug(
          "hasRfqRows: {}, hasListLinks: {}, hasProfileName: {}, hasContentContainer: {}",
          hasRfqRows,
          hasListLinks,
          hasProfileName,
          hasContentContainer);
      return false;
    }
  }

  /**
   * Extract basic statistics from the HTML response
   *
   * @param htmlResponse The HTML response to analyze
   * @return Basic statistics about the search results
   */
  public SearchStatistics extractSearchStatistics(String htmlResponse) {
    if (htmlResponse == null) {
      return new SearchStatistics(0, false);
    }

    // Count tender rows
    int tenderCount = 0;
    int index = 0;
    while ((index = htmlResponse.indexOf("class=\"rfqRow\"", index)) != -1) {
      tenderCount++;
      index += 14; // Length of "class=\"rfqRow\""
    }

    boolean hasMoreResults =
        htmlResponse.contains("weitere Ergebnisse")
            || htmlResponse.contains("more results")
            || htmlResponse.contains("nächste Seite");

    return new SearchStatistics(tenderCount, hasMoreResults);
  }

  /** Simple statistics class for search results */
  public static class SearchStatistics {
    private final int tenderCount;
    private final boolean hasMoreResults;

    public SearchStatistics(int tenderCount, boolean hasMoreResults) {
      this.tenderCount = tenderCount;
      this.hasMoreResults = hasMoreResults;
    }

    public int getTenderCount() {
      return tenderCount;
    }

    public boolean hasMoreResults() {
      return hasMoreResults;
    }

    @Override
    public String toString() {
      return String.format(
          "SearchStatistics{tenderCount=%d, hasMoreResults=%s}", tenderCount, hasMoreResults);
    }
  }
}
