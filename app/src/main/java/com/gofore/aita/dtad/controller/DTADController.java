package com.gofore.aita.dtad.controller;

import com.gofore.aita.dtad.model.DTADTender;
import com.gofore.aita.dtad.service.DTADService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/dtad")
@Slf4j
public class DTADController {

  @Autowired private DTADService dtadService;

  /** Search for tenders using default configured profiles */
  @GetMapping("/tenders")
  public ResponseEntity<?> searchTenders() {
    try {
      log.info("Received request to search DTAD tenders with default profiles");

      List<DTADTender> tenders = dtadService.searchTendersDefault();

      log.info("Successfully retrieved {} tenders from DTAD", tenders.size());
      return ResponseEntity.ok(tenders);

    } catch (Exception e) {
      log.error("Error searching DTAD tenders: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Error searching tenders: " + e.getMessage());
    }
  }

  /** Search for tenders using a specific profile */
  @GetMapping("/tenders/profile/{profileId}")
  public ResponseEntity<?> searchTendersByProfile(@PathVariable String profileId) {
    try {
      log.info("Received request to search DTAD tenders with profile: {}", profileId);

      List<DTADTender> tenders = dtadService.searchTenders(profileId);

      log.info("Successfully retrieved {} tenders from DTAD profile {}", tenders.size(), profileId);
      return ResponseEntity.ok(tenders);

    } catch (Exception e) {
      log.error("Error searching DTAD tenders with profile {}: {}", profileId, e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Error searching tenders: " + e.getMessage());
    }
  }



  /** Test DTAD connection and authentication */
  @GetMapping("/test")
  public ResponseEntity<?> testConnection() {
    try {
      log.info("Received request to test DTAD connection");

      boolean success = dtadService.testConnection();

      if (success) {
        return ResponseEntity.ok("DTAD connection test successful");
      } else {
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
            .body("DTAD connection test failed");
      }

    } catch (Exception e) {
      log.error("Error testing DTAD connection: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Error testing connection: " + e.getMessage());
    }
  }

  /** Get current session status */
  @GetMapping("/session/status")
  public ResponseEntity<?> getSessionStatus() {
    try {
      log.debug("Received request for DTAD session status");

      DTADService.SessionStatus status = dtadService.getSessionStatus();

      return ResponseEntity.ok(status);

    } catch (Exception e) {
      log.error("Error getting DTAD session status: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Error getting session status: " + e.getMessage());
    }
  }

  /** Health check endpoint */
  @GetMapping("/health")
  public ResponseEntity<String> healthCheck() {
    return ResponseEntity.ok("DTAD service is running");
  }
}
