package com.gofore.aita.dtad.service;

import com.gofore.aita.dtad.model.DTADTender;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DTADHtmlParser {

  private static final DateTimeFormatter GERMAN_DATE_FORMAT =
      DateTimeFormatter.ofPattern("dd.MM.yyyy");
  private static final Pattern TENDER_ID_PATTERN = Pattern.compile("rfq(\\d+)");
  private static final Pattern POSTAL_CODE_PATTERN = Pattern.compile("\\b\\d{5}\\b");

  /**
   * Parse HTML response and extract tender information
   *
   * @param htmlResponse The HTML response from DTAD search
   * @param profileId The search profile ID used
   * @return List of extracted tenders
   */
  public List<DTADTender> parseSearchResults(String htmlResponse, String profileId) {
    log.info(
        "Parsing DTAD search results HTML (length: {})",
        htmlResponse != null ? htmlResponse.length() : 0);

    if (htmlResponse == null || htmlResponse.trim().isEmpty()) {
      log.warn("HTML response is null or empty");
      return new ArrayList<>();
    }

    List<DTADTender> tenders = new ArrayList<>();

    try {
      Document doc = Jsoup.parse(htmlResponse);

      // Find all tender rows
      Elements tenderRows = doc.select("tr.rfqRow");
      log.info("Found {} tender rows in HTML", tenderRows.size());

      for (Element row : tenderRows) {
        try {
          DTADTender tender = parseTenderRow(row, profileId);
          if (tender != null) {
            tenders.add(tender);
          }
        } catch (Exception e) {
          log.error("Error parsing tender row: {}", e.getMessage(), e);
        }
      }

      log.info("Successfully parsed {} tenders from HTML", tenders.size());

    } catch (Exception e) {
      log.error("Error parsing HTML response: {}", e.getMessage(), e);
    }

    return tenders;
  }

  /** Parse a single tender row element */
  private DTADTender parseTenderRow(Element row, String profileId) {
    try {
      DTADTender.DTADTenderBuilder builder = DTADTender.builder();

      // Extract tender ID from row ID attribute
      String rowId = row.attr("id");
      if (rowId != null && rowId.startsWith("rfq")) {
        String tenderNumber = rowId.substring(3); // Remove "rfq" prefix
        builder.tenderNumber(tenderNumber);

        // Look for the full tender ID (with suffix)
        Elements checkboxes = row.select("input[name=rfqIds]");
        if (!checkboxes.isEmpty()) {
          String fullId = checkboxes.first().attr("value");
          builder.tenderId(fullId);
        } else {
          builder.tenderId(tenderNumber + "_1"); // Default suffix
        }
      }

      // Extract title and description from listLink
      Elements titleLinks = row.select("span.listLink");
      if (!titleLinks.isEmpty()) {
        Element titleLink = titleLinks.first();
        String title = titleLink.attr("title");
        String linkText = titleLink.text();

        if (title != null && !title.trim().isEmpty()) {
          // Title attribute often contains full description
          builder.title(extractTitleFromFullDescription(title));
          builder.fullDescription(title);
        }

        if (linkText != null && !linkText.trim().isEmpty()) {
          builder.shortDescription(linkText.trim());
          if (builder.build().getTitle() == null) {
            builder.title(linkText.trim());
          }
        }
      }

      // Extract tender type from images
      Elements typeImages = row.select("img[alt*='Ausschreibung']");
      if (!typeImages.isEmpty()) {
        String alt = typeImages.first().attr("alt");
        builder.tenderType(alt);
      }

      // Extract document type
      Elements docTypeImages = row.select("img[src*='rfq_doctype']");
      if (!docTypeImages.isEmpty()) {
        String alt = docTypeImages.first().attr("alt");
        builder.documentType(alt);
      }

      // Parse detailed information from description rows
      parseDetailedInfo(row, builder);

      // Set metadata
      builder
          .extractedAt(LocalDateTime.now())
          .extractionSource("DTAD")
          .profileId(profileId)
          .status(DTADTender.TenderStatus.ACTIVE); // Default to active

      DTADTender tender = builder.build();

      // Validate that we have minimum required information
      if (tender.getTenderId() != null && tender.getDisplayTitle() != null) {
        log.debug("Parsed tender: {}", tender.getSummary());
        return tender;
      } else {
        log.warn(
            "Skipping tender with insufficient data: tenderId={}, title={}",
            tender.getTenderId(),
            tender.getDisplayTitle());
        return null;
      }

    } catch (Exception e) {
      log.error("Error parsing tender row: {}", e.getMessage(), e);
      return null;
    }
  }

  /** Parse detailed information from tender description rows */
  private void parseDetailedInfo(Element mainRow, DTADTender.DTADTenderBuilder builder) {
    try {
      // Look for description rows that follow this tender row
      String tenderId = builder.build().getTenderId();
      if (tenderId == null) return;

      String tenderNumber = tenderId.split("_")[0];

      // Find all description rows for this tender
      Elements descRows = mainRow.parent().select("tr.short_desc_" + tenderId + ", tr.rfqDesc");

      for (Element descRow : descRows) {
        parseDescriptionRow(descRow, builder);
      }

    } catch (Exception e) {
      log.debug("Error parsing detailed info: {}", e.getMessage());
    }
  }

  /** Parse individual description row for specific information */
  private void parseDescriptionRow(Element row, DTADTender.DTADTenderBuilder builder) {
    try {
      Elements cells = row.select("td");

      for (int i = 0; i < cells.size() - 1; i++) {
        Element labelCell = cells.get(i);
        Element valueCell = cells.get(i + 1);

        String label = labelCell.text().trim();
        String value = valueCell.text().trim();

        if (label.isEmpty() || value.isEmpty()) continue;

        // Parse specific fields based on label
        if (label.contains("Auftragsart")) {
          builder.tenderType(value);
        } else if (label.contains("Frist Angebotsabgabe")) {
          LocalDate deadline = parseGermanDate(value);
          if (deadline != null) {
            builder.submissionDeadline(deadline);
          }
        } else if (label.contains("Zeitvertrag endet")) {
          LocalDate endDate = parseGermanDate(value);
          if (endDate != null) {
            builder.contractEndDate(endDate);
          }
        } else if (label.contains("Ort") || label.contains("Region")) {
          parseLocationInfo(value, builder);
        } else if (label.contains("Auftraggeber") || label.contains("Organisation")) {
          builder.contractingAuthority(value);
        } else if (label.contains("Wert") || label.contains("Volumen")) {
          builder.estimatedValue(value);
        } else if (label.contains("CPV")) {
          builder.cpvCode(value);
        }
      }

    } catch (Exception e) {
      log.debug("Error parsing description row: {}", e.getMessage());
    }
  }

  /** Parse German date format (dd.MM.yyyy) */
  private LocalDate parseGermanDate(String dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty()) {
      return null;
    }

    try {
      // Extract date pattern from text
      Pattern datePattern = Pattern.compile("\\d{2}\\.\\d{2}\\.\\d{4}");
      Matcher matcher = datePattern.matcher(dateStr);

      if (matcher.find()) {
        String dateMatch = matcher.group();
        return LocalDate.parse(dateMatch, GERMAN_DATE_FORMAT);
      }
    } catch (DateTimeParseException e) {
      log.debug("Could not parse date: {}", dateStr);
    }

    return null;
  }

  /** Parse location information and extract city, postal code */
  private void parseLocationInfo(String locationStr, DTADTender.DTADTenderBuilder builder) {
    if (locationStr == null || locationStr.trim().isEmpty()) {
      return;
    }

    // Extract postal code
    Matcher postalMatcher = POSTAL_CODE_PATTERN.matcher(locationStr);
    if (postalMatcher.find()) {
      builder.postalCode(postalMatcher.group());
    }

    // Set full location string
    builder.location(locationStr.trim());

    // Default country for DTAD
    builder.country("DE");
  }

  /** Extract clean title from full description */
  private String extractTitleFromFullDescription(String fullDescription) {
    if (fullDescription == null || fullDescription.trim().isEmpty()) {
      return null;
    }

    // Remove "Ausschreibung - " prefix if present
    String title = fullDescription.trim();
    if (title.startsWith("Ausschreibung - ")) {
      title = title.substring(16);
    }

    // Remove location suffix (usually " in CityName")
    int inIndex = title.lastIndexOf(" in ");
    if (inIndex > 0 && inIndex > title.length() / 2) {
      title = title.substring(0, inIndex);
    }

    return title.trim();
  }
}
