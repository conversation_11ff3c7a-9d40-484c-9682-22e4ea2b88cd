package com.gofore.aita.dtad.service;

import com.gofore.aita.dtad.model.DTADTender;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DTADHtmlParser {

  /**
   * Parse HTML response and extract tender information
   * 
   * @param htmlResponse The HTML response from DTAD search
   * @param profileId The search profile ID used
   * @return List of extracted tenders
   */
  public List<DTADTender> parseSearchResults(String htmlResponse, String profileId) {
    log.info("Parsing DTAD search results HTML (length: {})", htmlResponse != null ? htmlResponse.length() : 0);
    
    if (htmlResponse == null || htmlResponse.trim().isEmpty()) {
      log.warn("HTML response is null or empty");
      return new ArrayList<>();
    }

    List<DTADTender> tenders = new ArrayList<>();
    
    try {
      Document doc = Jsoup.parse(htmlResponse);
      
      // Find the main table containing tenders
      Elements tables = doc.select("table.rfqTableList");
      if (tables.isEmpty()) {
        log.warn("No table with class 'rfqTableList' found, trying to find rfqRow elements directly");
        // Fallback: look for rfqRow elements anywhere in the document
        return parseRfqRowsDirectly(doc, profileId);
      }
      
      Element table = tables.first();
      
      // Find all tender rows (first row of each tender)
      Elements rfqRows = table.select("tr.rfqRow");
      log.info("Found {} tender rows (rfqRow) in table", rfqRows.size());
      
      for (Element rfqRow : rfqRows) {
        try {
          DTADTender tender = parseTenderFromRfqRow(rfqRow, profileId);
          if (tender != null) {
            tenders.add(tender);
          }
        } catch (Exception e) {
          log.error("Error parsing tender row: {}", e.getMessage(), e);
        }
      }
      
      log.info("Successfully parsed {} tenders from HTML", tenders.size());
      
    } catch (Exception e) {
      log.error("Error parsing HTML response: {}", e.getMessage(), e);
    }
    
    return tenders;
  }

  /**
   * Fallback method to parse rfqRow elements directly from document
   */
  private List<DTADTender> parseRfqRowsDirectly(Document doc, String profileId) {
    List<DTADTender> tenders = new ArrayList<>();
    Elements rfqRows = doc.select("tr.rfqRow");
    log.info("Found {} rfqRow elements directly in document", rfqRows.size());

    for (Element rfqRow : rfqRows) {
      try {
        DTADTender tender = parseTenderFromRfqRow(rfqRow, profileId);
        if (tender != null) {
          tenders.add(tender);
        }
      } catch (Exception e) {
        log.error("Error parsing tender row: {}", e.getMessage(), e);
      }
    }

    return tenders;
  }

  /**
   * Parse a single tender starting from an rfqRow element
   */
  private DTADTender parseTenderFromRfqRow(Element rfqRow, String profileId) {
    try {
      DTADTender.DTADTenderBuilder builder = DTADTender.builder();
      
      // Extract data from the first row (rfqRow)
      parseFirstRow(rfqRow, builder);
      
      // Find and parse subsequent rows belonging to this tender
      parseSubsequentRows(rfqRow, builder);
      
      // Set metadata
      builder.extractedAt(LocalDateTime.now())
             .extractionSource("DTAD")
             .profileId(profileId);
      
      DTADTender tender = builder.build();
      
      // Validate that we have minimum required information
      if (tender.getDtadId() != null || tender.getTitle() != null) {
        log.debug("Parsed tender: {}", tender.getSummary());
        return tender;
      } else {
        log.warn("Skipping tender with insufficient data: dtadId={}, title={}", 
                tender.getDtadId(), tender.getTitle());
        return null;
      }
      
    } catch (Exception e) {
      log.error("Error parsing tender from rfqRow: {}", e.getMessage(), e);
      return null;
    }
  }

  /**
   * Parse data from the first row (rfqRow)
   */
  private void parseFirstRow(Element rfqRow, DTADTender.DTADTenderBuilder builder) {
    // Extract title from span.listLink
    Elements titleElements = rfqRow.select("span.listLink");
    if (!titleElements.isEmpty()) {
      String title = titleElements.first().text().trim();
      if (!title.isEmpty()) {
        builder.title(title);
      }
    }

    // Extract data from td.list-column1 elements
    Elements columns = rfqRow.select("td.list-column1");

    // Parse columns in order, looking for specific patterns
    String region = null;
    String datum = null;
    String frist = null;

    for (Element column : columns) {
      String text = column.text().trim();

      if (text.isEmpty()) continue;

      // Check if this looks like a region (contains digits and letters, like "20459 Hamburg")
      if (text.matches(".*\\d{5}.*") && text.length() > 5 && region == null) {
        region = text;
      }
      // Check if this looks like a date (dd.MM.yyyy format)
      else if (text.matches("\\d{2}\\.\\d{2}\\.\\d{4}")) {
        // First date found is usually datum, second is frist
        if (datum == null) {
          datum = text;
        } else if (frist == null) {
          frist = text;
        }
      }
    }

    // Set the extracted values
    if (region != null) builder.region(region);
    if (datum != null) builder.datum(datum);
    if (frist != null) builder.frist(frist);
  }

  /**
   * Parse subsequent rows that belong to this tender
   */
  private void parseSubsequentRows(Element rfqRow, DTADTender.DTADTenderBuilder builder) {
    // Get the tender ID from the rfqRow to find related rows
    String rowId = rfqRow.attr("id");
    if (rowId == null || !rowId.startsWith("rfq")) {
      return;
    }

    String tenderId = rowId.substring(3); // Remove "rfq" prefix
    String tenderIdWithSuffix = tenderId + "_1"; // Most common suffix

    // Find all subsequent rows with class containing this tender ID
    Element parent = rfqRow.parent();
    if (parent == null) return;

    // Be more specific - only select rows that belong to this specific tender
    Elements subsequentRows = parent.select("tr.short_desc_" + tenderIdWithSuffix);

    log.debug("Found {} subsequent rows for tender {} (looking for short_desc_{})",
              subsequentRows.size(), tenderId, tenderIdWithSuffix);

    for (Element row : subsequentRows) {
      parseDetailRow(row, builder);
    }
  }

  /**
   * Parse a detail row containing key-value pairs
   */
  private void parseDetailRow(Element row, DTADTender.DTADTenderBuilder builder) {
    Elements cells = row.select("td");
    
    for (int i = 0; i < cells.size(); i++) {
      Element cell = cells.get(i);
      String cellText = cell.text().trim();
      
      // Look for bold keys (they contain the field names with ":")
      Elements boldElements = cell.select("b");
      if (!boldElements.isEmpty()) {
        String key = boldElements.first().text().trim();
        
        // Find the corresponding value in the next cell or same row
        String value = findValueForKey(cells, i, key);
        
        if (value != null && !value.isEmpty()) {
          mapKeyValueToBuilder(key, value, builder);
        }
      }
      // Also check for keys without bold formatting
      else if (cellText.endsWith(":")) {
        String key = cellText;
        String value = findValueForKey(cells, i, key);
        
        if (value != null && !value.isEmpty()) {
          mapKeyValueToBuilder(key, value, builder);
        }
      }
    }
  }

  /**
   * Find the value for a given key in the row
   */
  private String findValueForKey(Elements cells, int keyIndex, String key) {
    // Try next cell first
    if (keyIndex + 1 < cells.size()) {
      String nextCellText = cells.get(keyIndex + 1).text().trim();
      if (!nextCellText.isEmpty() && !nextCellText.endsWith(":")) {
        return nextCellText;
      }
    }
    
    // Try cells further to the right
    for (int i = keyIndex + 2; i < cells.size(); i++) {
      String cellText = cells.get(i).text().trim();
      if (!cellText.isEmpty() && !cellText.endsWith(":")) {
        return cellText;
      }
    }
    
    return null;
  }

  /**
   * Map key-value pairs to the builder
   */
  private void mapKeyValueToBuilder(String key, String value, DTADTender.DTADTenderBuilder builder) {
    key = key.toLowerCase().replace(":", "").trim();
    
    switch (key) {
      case "dtad-id":
        builder.dtadId(value);
        break;
      case "auftragsart":
        builder.auftragsart(value);
        break;
      case "frist angebotsabgabe":
        builder.fristAngebotsabgabe(value);
        break;
      case "dokumententyp":
        builder.dokumententyp(value);
        break;
      case "zeitvertrag endet":
        builder.zeitvertragEndet(value);
        break;
      case "beschreibung":
        // Clean up HTML entities and tags
        String cleanDescription = value.replace("\\<br>", "\n").replace("<br>", "\n");
        builder.beschreibung(cleanDescription);
        break;
      case "auftraggeber":
      case "vergabestelle":
        builder.auftraggeber(value);
        break;
      case "erfüllungsort":
        builder.erfuellungsort(value);
        break;
      default:
        log.debug("Unknown key: {} with value: {}", key, value);
        break;
    }
  }
}
