package com.gofore.aita.dtad.service;

import com.gofore.aita.dtad.model.DTADTender;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DTADService {

  @Autowired private DTADAuthService authService;

  @Autowired private DTADSearchService searchService;

  @Autowired private DTADHtmlParser htmlParser;

  @Value("${app.dtad.default-profile-ids:}")
  private String defaultProfileIds;

  /**
   * Search for tenders using a specific search profile
   *
   * @param profileId The search profile ID to execute
   * @return List of tenders found
   */
  public List<DTADTender> searchTenders(String profileId) throws Exception {
    log.info("Starting DTAD tender search with profile: {}", profileId);

    try {
      // Execute search profile
      String htmlResponse = searchService.executeSearchProfileWithRetry(profileId, 3);

      // Validate response
      if (!searchService.validateSearchResponse(htmlResponse)) {
        log.warn("Search response validation failed for profile: {}", profileId);
        return new ArrayList<>();
      }

      // Extract statistics
      DTADSearchService.SearchStatistics stats =
          searchService.extractSearchStatistics(htmlResponse);
      log.info("Search completed - found {} tenders: {}", stats.getTenderCount(), stats);

      // Parse HTML and extract tender data
      List<DTADTender> tenders = htmlParser.parseSearchResults(htmlResponse, profileId);

      log.info("Successfully extracted {} tenders from DTAD search", tenders.size());
      return tenders;

    } catch (Exception e) {
      log.error("Error searching DTAD tenders with profile {}: {}", profileId, e.getMessage(), e);
      throw e;
    }
  }

  /**
   * Search for tenders using multiple search profiles
   *
   * @param profileIds Array of search profile IDs to execute
   * @return Combined list of tenders from all profiles
   */
  public List<DTADTender> searchTendersMultipleProfiles(String[] profileIds) throws Exception {
    log.info(
        "Starting DTAD tender search with {} profiles: {}",
        profileIds.length,
        Arrays.toString(profileIds));

    List<DTADTender> allTenders = new ArrayList<>();

    try {
      // Execute all search profiles
      String[] htmlResponses = searchService.executeMultipleSearchProfiles(profileIds);

      // Process each response
      for (int i = 0; i < profileIds.length; i++) {
        String profileId = profileIds[i];
        String htmlResponse = htmlResponses[i];

        if (htmlResponse == null) {
          log.warn("No response received for profile: {}", profileId);
          continue;
        }

        try {
          // Validate and parse response
          if (searchService.validateSearchResponse(htmlResponse)) {
            List<DTADTender> tenders = htmlParser.parseSearchResults(htmlResponse, profileId);
            allTenders.addAll(tenders);
            log.info("Profile {} contributed {} tenders", profileId, tenders.size());
          } else {
            log.warn("Invalid response for profile: {}", profileId);
          }
        } catch (Exception e) {
          log.error("Error processing response for profile {}: {}", profileId, e.getMessage());
        }
      }

      // Remove duplicates based on tender ID
      List<DTADTender> uniqueTenders = removeDuplicates(allTenders);

      log.info(
          "Successfully extracted {} unique tenders from {} profiles",
          uniqueTenders.size(),
          profileIds.length);
      return uniqueTenders;

    } catch (Exception e) {
      log.error("Error searching DTAD tenders with multiple profiles: {}", e.getMessage(), e);
      throw e;
    }
  }

  /**
   * Search for tenders using default configured profiles
   *
   * @return List of tenders found using default profiles
   */
  public List<DTADTender> searchTendersDefault() throws Exception {
    if (defaultProfileIds == null || defaultProfileIds.trim().isEmpty()) {
      throw new IllegalStateException(
          "No default profile IDs configured. Set app.dtad.default-profile-ids property.");
    }

    String[] profileIds = defaultProfileIds.split(",");
    for (int i = 0; i < profileIds.length; i++) {
      profileIds[i] = profileIds[i].trim();
    }

    log.info("Using default profile IDs: {}", Arrays.toString(profileIds));
    return searchTendersMultipleProfiles(profileIds);
  }

  /**
   * Test authentication and basic connectivity
   *
   * @return true if authentication and basic operations work
   */
  public boolean testConnection() {
    try {
      log.info("Testing DTAD connection and authentication");

      // Test authentication
      boolean authSuccess = authService.authenticate();
      if (!authSuccess) {
        log.error("DTAD authentication test failed");
        return false;
      }

      // Test keep-alive
      boolean keepAliveSuccess = authService.performKeepAlive();
      if (!keepAliveSuccess) {
        log.warn("DTAD keep-alive test failed, but authentication succeeded");
      }

      log.info("DTAD connection test successful");
      return true;

    } catch (Exception e) {
      log.error("DTAD connection test failed: {}", e.getMessage(), e);
      return false;
    }
  }

  /**
   * Get current session status
   *
   * @return Session status information
   */
  public SessionStatus getSessionStatus() {
    try {
      boolean hasValidSession = authService.getValidSession();
      boolean keepAliveWorks = authService.performKeepAlive();

      return new SessionStatus(
          hasValidSession, keepAliveWorks, authService.getCurrentSession() != null);
    } catch (Exception e) {
      log.error("Error checking session status: {}", e.getMessage());
      return new SessionStatus(false, false, false);
    }
  }

  /** Remove duplicate tenders based on tender ID */
  private List<DTADTender> removeDuplicates(List<DTADTender> tenders) {
    List<DTADTender> uniqueTenders = new ArrayList<>();
    List<String> seenIds = new ArrayList<>();

    for (DTADTender tender : tenders) {
      String uniqueId = tender.getUniqueId();
      if (uniqueId != null && !seenIds.contains(uniqueId)) {
        uniqueTenders.add(tender);
        seenIds.add(uniqueId);
      }
    }

    if (tenders.size() != uniqueTenders.size()) {
      log.info("Removed {} duplicate tenders", tenders.size() - uniqueTenders.size());
    }

    return uniqueTenders;
  }

  /** Session status information */
  public static class SessionStatus {
    private final boolean hasValidSession;
    private final boolean keepAliveWorks;
    private final boolean sessionExists;

    public SessionStatus(boolean hasValidSession, boolean keepAliveWorks, boolean sessionExists) {
      this.hasValidSession = hasValidSession;
      this.keepAliveWorks = keepAliveWorks;
      this.sessionExists = sessionExists;
    }

    public boolean hasValidSession() {
      return hasValidSession;
    }

    public boolean keepAliveWorks() {
      return keepAliveWorks;
    }

    public boolean sessionExists() {
      return sessionExists;
    }

    @Override
    public String toString() {
      return String.format(
          "SessionStatus{valid=%s, keepAlive=%s, exists=%s}",
          hasValidSession, keepAliveWorks, sessionExists);
    }
  }
}
