# Test Structure

This directory contains the tests for the application. The tests are organized as follows:

## Directory Structure

```
test/
├── java/
│   └── com/
│       └── gofore/
│           └── aita/
│               ├── BaseTest.java                  # Base test class with common setup
│               ├── mocks/                         # Mock implementations for testing
│               │   ├── MockOpenAIService.java     # Mock for OpenAI service
│               │   ├── MockStorageAccountFileStore.java # Mock for file storage
│               │   └── TestAzureConfig.java       # Test configuration for Azure services
│               ├── tender/                        # Tests for tender module
│               │   └── api/                       # Tests for tender API
│               │       ├── TenderControllerTest.java  # Unit tests for TenderController
│               │       ├── TenderControllerITest.java # Integration tests for TenderController
│               │       └── util/                  # Utility classes for tender API tests
│               │           └── CredentialHelperTest.java # Tests for CredentialHelper
│               └── utils/                         # Common test utilities
│                   └── TestUtils.java             # Utility methods for tests
└── resources/
    ├── application-test.yml                       # Test configuration
```

## Test Types

### Unit Tests

Unit tests focus on testing individual components in isolation. They should be fast and not depend on external resources.

Key characteristics of our unit tests:
- Complete isolation of the component being tested
- All dependencies are mocked using `@MockitoBean`
- Focus on testing a single component's logic
- Controlled inputs and outputs
- Fast execution

### Integration Tests

Integration tests verify that different components work together correctly. They may use mocked external dependencies.

Key characteristics of our integration tests:
- Test how components interact with each other
- Some components are autowired rather than mocked
- Test more complete flows through the application
- Use embedded MongoDB for database operations
- More realistic test scenarios

## Test Utilities and Mocks

### BaseTest

The `BaseTest` class provides common setup and utilities for all tests. It includes:

- MockMvc for testing controllers
- Common mocks for repositories and services (MongoDB, Azure services)
- Setup and teardown methods
- Configuration for the test environment

### TestUtils

The `TestUtils` class provides helper methods for creating test data and common test objects:

- Creating test HTTP headers with authentication information
- Creating test users and UserDTOs
- Creating test tenders with all required fields
- Creating mock multipart files for file upload testing

### MockStorageAccountFileStore

The `MockStorageAccountFileStore` class provides an in-memory implementation of the `IFileStore` interface for testing file operations without requiring actual file storage. It stores files in memory and provides methods for retrieving and manipulating them.

### MockOpenAIService

The `MockOpenAIService` class provides a mock implementation of the `IAnalysisService` interface for testing analysis functionality without requiring actual OpenAI API calls.

## Best Practices

1. **Use the BaseTest class** for common setup and utilities
2. **Use TestUtils** for creating test data
3. **Keep tests independent** - each test should be able to run independently
4. **Clean up after tests** - use `@BeforeEach` and `@AfterEach` to set up and clean up test data
5. **Use meaningful test names** - test names should describe what is being tested
6. **Follow the AAA pattern** - Arrange, Act, Assert
7. **Test both happy path and error cases**
8. **Use the @ActiveProfiles("test") annotation** to ensure tests use the test configuration
9. **Provide complete test data** - ensure all required fields are included in test data
10. **Use proper annotations for DTOs** - ensure DTO classes have both `@AllArgsConstructor` and `@NoArgsConstructor` annotations for proper serialization/deserialization
11. **Mock external dependencies** - use `@MockitoBean` to mock external dependencies like MongoDB and Azure services

