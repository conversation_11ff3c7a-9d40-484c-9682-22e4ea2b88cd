# Extraction Test Files

This directory contains test files used for testing the document extraction functionality.

## Files

- **extraction_test_valid_file.pdf**: A valid PDF file used to test the successful extraction of text using Apache Tika in the `TikaDocumentExtractor` class.

- **extraction_test_corrupted_file.pdf**: A deliberately corrupted PDF file used to test the error handling capabilities of the `TikaDocumentExtractor` class. This file has a valid PDF header but invalid content that causes Tika to fail during parsing.

These files are specifically used in `TikaDocumentExtractorTest` to verify that the document extraction functionality works correctly for both valid and invalid files.
