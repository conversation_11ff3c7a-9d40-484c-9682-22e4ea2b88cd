package com.gofore.aita.extraction.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.gofore.aita.extraction.data.ExtractedDocumentRepository;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.extraction.models.ExtractionStatus;
import com.gofore.aita.tender.domain.models.FileMetadata;
import com.gofore.aita.tender.domain.models.Tender;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FileExtractionServiceTest {

  @Mock private ExtractionService extractionService;

  @Mock private ExtractedDocumentRepository extractedDocumentRepository;

  @InjectMocks private FileExtractionService fileExtractionService;

  private Tender tender;
  private FileMetadata pdfFile;
  private FileMetadata docxFile;
  private FileMetadata zipFile;
  private ExtractedDocument extractedPdfDocument;

  @BeforeEach
  void setUp() {
    // Set up test data
    tender = new Tender();
    tender.setId("tender-123");

    pdfFile = new FileMetadata();
    pdfFile.setId("pdf-file-id");
    pdfFile.setFileName("document.pdf");
    pdfFile.setFilePath("tenders/tender-123/document.pdf");

    docxFile = new FileMetadata();
    docxFile.setId("docx-file-id");
    docxFile.setFileName("document.docx");
    docxFile.setFilePath("tenders/tender-123/document.docx");

    zipFile = new FileMetadata();
    zipFile.setId("zip-file-id");
    zipFile.setFileName("archive.zip");
    zipFile.setFilePath("tenders/tender-123/archive.zip");

    extractedPdfDocument =
        ExtractedDocument.builder()
            .id("extracted-pdf-id")
            .fileId(pdfFile.getId())
            .fileName(pdfFile.getFileName())
            .tenderId(tender.getId())
            .text("Extracted PDF text")
            .tokenCount(100)
            .status(ExtractionStatus.SUCCESS)
            .build();
  }

  @Test
  void processFilesForExtraction_ShouldReturnEmptyList_WhenNoFiles() {
    // Arrange
    tender.setFiles(Collections.emptyList());

    // Act
    List<ExtractedDocument> result = fileExtractionService.processFilesForExtraction(tender);

    // Assert
    assertTrue(result.isEmpty());
    verifyNoInteractions(extractedDocumentRepository, extractionService);
  }

  @Test
  void processFilesForExtraction_ShouldUseExistingExtraction_WhenAvailable() {
    // Arrange
    tender.setFiles(Collections.singletonList(pdfFile));
    when(extractedDocumentRepository.findByFileId(pdfFile.getId()))
        .thenReturn(Optional.of(extractedPdfDocument));

    // Act
    List<ExtractedDocument> result = fileExtractionService.processFilesForExtraction(tender);

    // Assert
    assertEquals(1, result.size());
    assertEquals(extractedPdfDocument, result.get(0));
    verify(extractedDocumentRepository).findByFileId(pdfFile.getId());
    verifyNoInteractions(extractionService);
  }

  @Test
  void processFilesForExtraction_ShouldExtractText_WhenNoExistingExtraction() {
    // Arrange
    tender.setFiles(Collections.singletonList(docxFile));
    when(extractedDocumentRepository.findByFileId(docxFile.getId())).thenReturn(Optional.empty());

    ExtractedDocument extractedDocxDocument =
        ExtractedDocument.builder()
            .id("extracted-docx-id")
            .fileId(docxFile.getId())
            .fileName(pdfFile.getFileName())
            .tenderId(tender.getId())
            .text("Extracted DOCX text")
            .tokenCount(150)
            .status(ExtractionStatus.SUCCESS)
            .build();

    when(extractionService.startExtraction(tender.getId(), docxFile))
        .thenReturn(extractedDocxDocument);
    when(extractedDocumentRepository.save(extractedDocxDocument)).thenReturn(extractedDocxDocument);

    // Act
    List<ExtractedDocument> result = fileExtractionService.processFilesForExtraction(tender);

    // Assert
    assertEquals(1, result.size());
    assertEquals(extractedDocxDocument, result.get(0));
    verify(extractedDocumentRepository).findByFileId(docxFile.getId());
    verify(extractionService).startExtraction(tender.getId(), docxFile);
  }

  @Test
  void processFilesForExtraction_ShouldSkipUnsupportedFiles() {
    // Arrange
    tender.setFiles(Arrays.asList(pdfFile, zipFile));
    when(extractedDocumentRepository.findByFileId(pdfFile.getId()))
        .thenReturn(Optional.of(extractedPdfDocument));

    // Act
    List<ExtractedDocument> result = fileExtractionService.processFilesForExtraction(tender);

    // Assert
    assertEquals(1, result.size());
    assertEquals(extractedPdfDocument, result.get(0));
    verify(extractedDocumentRepository).findByFileId(pdfFile.getId());
    verifyNoInteractions(extractionService);
  }

  @Test
  void processFilesForExtraction_ShouldSkipFailedExtractions() {
    // Arrange
    tender.setFiles(Collections.singletonList(docxFile));
    when(extractedDocumentRepository.findByFileId(docxFile.getId())).thenReturn(Optional.empty());

    ExtractedDocument failedDocument =
        ExtractedDocument.builder()
            .id("failed-extraction-id")
            .fileId(docxFile.getId())
            .fileName(pdfFile.getFileName())
            .tenderId(tender.getId())
            .text("")
            .tokenCount(0)
            .status(ExtractionStatus.FAILED)
            .build();

    when(extractionService.startExtraction(tender.getId(), docxFile)).thenReturn(failedDocument);

    // Act
    List<ExtractedDocument> result = fileExtractionService.processFilesForExtraction(tender);

    // Assert
    assertTrue(result.isEmpty());
    verify(extractedDocumentRepository).findByFileId(docxFile.getId());
    verify(extractionService).startExtraction(tender.getId(), docxFile);
  }

  @Test
  void processFilesForExtraction_ShouldContinue_WhenExceptionOccurs() {
    // Arrange
    FileMetadata errorFile = new FileMetadata();
    errorFile.setId("error-file-id");
    errorFile.setFileName("error.pdf");

    tender.setFiles(Arrays.asList(pdfFile, errorFile));

    when(extractedDocumentRepository.findByFileId(pdfFile.getId()))
        .thenReturn(Optional.of(extractedPdfDocument));

    when(extractedDocumentRepository.findByFileId(errorFile.getId()))
        .thenThrow(new RuntimeException("Test exception"));

    // Act
    List<ExtractedDocument> result = fileExtractionService.processFilesForExtraction(tender);

    // Assert
    assertEquals(1, result.size());
    assertEquals(extractedPdfDocument, result.get(0));
    verify(extractedDocumentRepository).findByFileId(pdfFile.getId());
    verify(extractedDocumentRepository).findByFileId(errorFile.getId());
  }
}
