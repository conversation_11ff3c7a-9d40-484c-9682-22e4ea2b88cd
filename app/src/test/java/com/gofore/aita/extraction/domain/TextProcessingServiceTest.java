package com.gofore.aita.extraction.domain;

import static org.junit.jupiter.api.Assertions.*;

import com.gofore.aita.extraction.domain.TextProcessingService.TextWithTokenCount;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.extraction.models.ExtractionStatus;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TextProcessingServiceTest {

  private TextProcessingService textProcessingService;
  private ExtractedDocument document1;
  private ExtractedDocument document2;

  @BeforeEach
  void setUp() {
    textProcessingService = new TextProcessingService();

    document1 =
        ExtractedDocument.builder()
            .id("doc1")
            .fileId("file1")
            .tenderId("tender1")
            .text("This is the first extracted document.")
            .tokenCount(10)
            .status(ExtractionStatus.SUCCESS)
            .build();

    document2 =
        ExtractedDocument.builder()
            .id("doc2")
            .fileId("file2")
            .fileName("file2Name")
            .tenderId("tender1")
            .text("This is the second extracted document with more content.")
            .tokenCount(15)
            .status(ExtractionStatus.SUCCESS)
            .build();
  }

  @Test
  void combineTexts_ShouldCombineDescriptionAndDocuments() {
    // Arrange
    String description = "This is the tender description.";
    List<ExtractedDocument> documents = Arrays.asList(document1, document2);

    // Act
    TextWithTokenCount result = textProcessingService.combineTexts(description, documents);

    // Assert
    String expectedText =
        "AUSSCHREIBUNGSBESCHREIBUNG:\n"
            + "This is the tender description.\n\n"
            + "EXTRAHIERTER TEXT DER AUSSCHREIBUNGSDOKUMENTE:\n\n"
            + "--- DOKUMENT 1: null ---\n\n"
            + "This is the first extracted document.\n\n"
            + "--- DOKUMENT 2: file2Name ---\n\n"
            + "This is the second extracted document with more content.\n\n";

    assertEquals(expectedText, result.getText());
    // 10 (doc1) + 15 (doc2) + tokens from description + buffer
    assertTrue(result.getTokenCount() > 25);
  }

  @Test
  void combineTexts_ShouldHandleNullDescription() {
    // Arrange
    List<ExtractedDocument> documents = Arrays.asList(document1, document2);

    // Act
    TextWithTokenCount result = textProcessingService.combineTexts(null, documents);

    // Assert
    String expectedText =
        "EXTRAHIERTER TEXT DER AUSSCHREIBUNGSDOKUMENTE:\n\n"
            + "--- DOKUMENT 1: null ---\n\n"
            + "This is the first extracted document.\n\n"
            + "--- DOKUMENT 2: file2Name ---\n\n"
            + "This is the second extracted document with more content.\n\n";

    assertEquals(expectedText, result.getText());
    // 10 (doc1) + 15 (doc2) + buffer
    assertTrue(result.getTokenCount() >= 25);
  }

  @Test
  void combineTexts_ShouldHandleEmptyDocuments() {
    // Arrange
    String description = "This is the tender description.";

    // Act
    TextWithTokenCount result =
        textProcessingService.combineTexts(description, Collections.emptyList());

    // Assert
    String expectedText = "AUSSCHREIBUNGSBESCHREIBUNG:\n" + "This is the tender description.\n\n";

    assertEquals(expectedText, result.getText());
  }
}
