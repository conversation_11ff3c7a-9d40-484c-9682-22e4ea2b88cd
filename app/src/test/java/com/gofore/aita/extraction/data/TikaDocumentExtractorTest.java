package com.gofore.aita.extraction.data;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.extraction.models.ExtractionStatus;
import com.gofore.aita.tender.data.IFileStore;
import com.gofore.aita.tender.domain.models.FileMetadata;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TikaDocumentExtractorTest {

  @Mock private IFileStore fileStore;

  private TikaDocumentExtractor extractor;
  private FileMetadata textFileMeta;
  private FileMetadata pdfFileMeta;
  private FileMetadata corruptedPdfMeta;

  @BeforeEach
  void setUp() {
    extractor = new TikaDocumentExtractor(fileStore);

    textFileMeta =
        new FileMetadata(
            "txt-123",
            "sample.txt", // <-- use .txt extension
            "sample.txt",
            "tenders/123/sample.txt",
            1024L);

    pdfFileMeta =
        new FileMetadata(
            "pdf-123",
            "extraction_test_valid_file.pdf",
            "extraction_test_valid_file.pdf",
            "tenders/123/extraction_test_valid_file.pdf",
            2048L);

    corruptedPdfMeta =
        new FileMetadata(
            "pdf-789",
            "extraction_test_corrupted_file.pdf",
            "extraction_test_corrupted_file.pdf",
            "tenders/123/extraction_test_corrupted_file.pdf",
            100L);
  }

  @Test
  void extract_ValidTextFile_ReturnsExtractedDocument() {
    // Arrange
    String sampleText = "This is a sample text document for testing extraction.";
    InputStream inputStream = new ByteArrayInputStream(sampleText.getBytes(StandardCharsets.UTF_8));
    when(fileStore.retrieveFile(textFileMeta.getFilePath())).thenReturn(inputStream);

    // Act
    ExtractedDocument result = extractor.extract(textFileMeta);

    // Assert
    assertNotNull(result);
    assertEquals(ExtractionStatus.SUCCESS, result.getStatus());
    assertEquals(textFileMeta.getId(), result.getFileId());

    String resultText = result.getText();
    assertTrue(resultText.contains(sampleText), "Markdown should contain the original text");
    assertTrue(result.getTokenCount() > 0);
  }

  @Test
  void extract_ValidPdfFile_ReturnsExtractedDocument() throws Exception {
    // Arrange: real PDF stream from resources
    File pdf =
        Paths.get("src", "test", "resources", "extraction", "extraction_test_valid_file.pdf")
            .toFile();
    assertTrue(pdf.exists(), "Sample PDF must exist");
    when(fileStore.retrieveFile(pdfFileMeta.getFilePath())).thenReturn(new FileInputStream(pdf));

    // Act
    ExtractedDocument result = extractor.extract(pdfFileMeta);

    // Assert
    assertNotNull(result);
    assertEquals(ExtractionStatus.SUCCESS, result.getStatus());
    assertEquals(pdfFileMeta.getId(), result.getFileId());

    // PDF extraction should yield at least one non-empty chunk
    assertFalse(result.getText().isEmpty());
    assertTrue(result.getTokenCount() > 0);
  }

  @Test
  void extract_NullFile_ReturnsFailedDocument() {
    ExtractedDocument res = extractor.extract(null);
    assertEquals(ExtractionStatus.FAILED, res.getStatus());
    assertNull(res.getFileId());
    assertTrue(res.getText().isEmpty());
    assertEquals(0, res.getTokenCount());
  }

  @Test
  void extract_FileNotFound_ReturnsFailedDocument() {
    when(fileStore.retrieveFile(textFileMeta.getFilePath())).thenReturn(null);
    ExtractedDocument res = extractor.extract(textFileMeta);
    assertEquals(ExtractionStatus.FAILED, res.getStatus());
    assertEquals(textFileMeta.getId(), res.getFileId());
    assertTrue(res.getText().isEmpty());
    assertEquals(0, res.getTokenCount());
  }

  @Test
  void extract_IOExceptionDuringExtraction_ReturnsFailedDocument() throws Exception {
    InputStream bad = mock(InputStream.class);
    doThrow(new IOException("boom")).when(bad).read(any(), anyInt(), anyInt());
    when(fileStore.retrieveFile(textFileMeta.getFilePath())).thenReturn(bad);

    ExtractedDocument res = extractor.extract(textFileMeta);
    assertEquals(ExtractionStatus.FAILED, res.getStatus());
    assertEquals(textFileMeta.getId(), res.getFileId());
    assertTrue(res.getText().isEmpty());
    assertEquals(0, res.getTokenCount());
  }

  @Test
  void extract_CorruptedPdf_ReturnsFailedDocument() throws Exception {
    File badPdf =
        Paths.get("src", "test", "resources", "extraction", "extraction_test_corrupted_file.pdf")
            .toFile();
    assertTrue(badPdf.exists());
    when(fileStore.retrieveFile(corruptedPdfMeta.getFilePath()))
        .thenReturn(new FileInputStream(badPdf));

    ExtractedDocument res = extractor.extract(corruptedPdfMeta);
    assertEquals(ExtractionStatus.FAILED, res.getStatus());
    assertEquals(corruptedPdfMeta.getId(), res.getFileId());
    assertTrue(res.getText().isEmpty());
    assertEquals(0, res.getTokenCount());
  }
}
