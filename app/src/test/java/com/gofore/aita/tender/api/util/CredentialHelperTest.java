package com.gofore.aita.tender.api.util;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.gofore.aita.tender.domain.models.ClientCredentials;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;

class CredentialHelperTest {

  @Test
  void extractUserInformation() {
    HttpHeaders httpHeaders = new HttpHeaders();
    String principalId = UUID.randomUUID().toString();
    String principalName = UUID.randomUUID().toString();
    String principal = "Base64EncodedPrincipal";
    httpHeaders.set(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_ID, principalId);
    httpHeaders.set(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_NAME, principalName);
    httpHeaders.set(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL, principal);

    ClientCredentials clientCredentials = CredentialHelper.extractUserInformation(httpHeaders);
    assertThat(clientCredentials.getPrincipalId(), is(equalTo(principalId)));
    assertThat(clientCredentials.getPrincipalName(), is(equalTo(principalName)));
    assertThat(clientCredentials.getPrincipal(), is(equalTo(principal)));
  }
}
