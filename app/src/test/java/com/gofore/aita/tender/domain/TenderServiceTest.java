package com.gofore.aita.tender.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.gofore.aita.analysis.api.IAnalysisService;
import com.gofore.aita.analysis.domain.TenderAnalysisService;
import com.gofore.aita.extraction.data.ExtractedDocumentRepository;
import com.gofore.aita.extraction.domain.FileExtractionService;
import com.gofore.aita.extraction.domain.TextProcessingService;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.models.Tender;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

@ExtendWith(MockitoExtension.class)
class TenderServiceTest {

  @Mock private TenderRepository tenderRepository;
  @Mock private FileStorageService fileStorageService;
  @Mock private ApplicationEventPublisher eventPublisher;
  @Mock private TenderAnalysisService tenderAnalysisService;
  @Mock private IAnalysisService analysisService;
  @Mock private FileExtractionService fileExtractionService;
  @Mock private TextProcessingService textProcessingService;
  @Mock private ExtractedDocumentRepository extractedDocumentRepository;

  private TenderService tenderService;
  private Tender tender;
  private com.gofore.aita.tender.domain.models.AnalysisResult analysisResult;

  @BeforeEach
  void setUp() {
    tenderService =
        new TenderService(
            fileStorageService,
            tenderRepository,
            eventPublisher,
            tenderAnalysisService,
            analysisService,
            fileExtractionService,
            textProcessingService,
            extractedDocumentRepository);

    // Set up test data
    tender = new Tender();
    tender.setId("tender-123");

    analysisResult = new com.gofore.aita.tender.domain.models.AnalysisResult();
    analysisResult.setAnalysisResult("Analysis result");
    analysisResult.setPromptTokens(50);
    analysisResult.setCompletionTokens(100);
  }

  @Test
  void startAnalysis_ShouldDelegateToTenderAnalysisService() {
    // Arrange
    when(tenderAnalysisService.analyzeTender(tender.getId())).thenReturn(analysisResult);

    // Act
    com.gofore.aita.tender.domain.models.AnalysisResult result =
        tenderService.startAnalysis(tender.getId());

    // Assert
    assertNotNull(result);
    assertEquals(analysisResult, result);
    verify(tenderAnalysisService).analyzeTender(tender.getId());
  }
}
