package com.gofore.aita.mocks;

import com.gofore.aita.analysis.api.IAnalysisService;
import com.gofore.aita.analysis.models.AnalysisResult;
import com.gofore.aita.analysis.models.TenderExtractionResult;
import java.util.List;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * Mock implementation of IAnalysisService for testing. This implementation returns predefined mock
 * results without calling the actual OpenAI service.
 */
@Component
@Primary
@Profile("test")
public class MockOpenAIService implements IAnalysisService {

  @Override
  public AnalysisResult analyzeTender(String tenderInformation) {
    return createMockResult("Mock analysis result for testing");
  }

  @Override
  public AnalysisResult analyzeTender(String tenderInformation, String customPrompt) {
    return createMockResult("Mock analysis result for testing with custom prompt: " + customPrompt);
  }

  @Override
  public TenderExtractionResult extractTenderFields(String tenderInformation) {
    TenderExtractionResult mockResult = new TenderExtractionResult();
    mockResult.setClient("Mock Client Organization");
    mockResult.setSubmissionDate("2024-12-31T23:59:59");
    mockResult.setBindingDeadline("30 days");
    mockResult.setContractDuration("12 months");
    mockResult.setPublicationDate("2024-01-01");
    mockResult.setQuestionDeadline("2024-12-15T17:00:00");
    mockResult.setContractValue(100000.0f);
    mockResult.setMaximumBudget("150 person-days");
    mockResult.setWinningCriteria("Best value for money");
    mockResult.setWeightingPriceQuality("70% quality, 30% price");
    mockResult.setDeliveryLocation("Remote/On-site hybrid");
    return mockResult;
  }

  private AnalysisResult createMockResult(String resultText) {
    AnalysisResult mockResult = new AnalysisResult();
    mockResult.setAnalysisResult(resultText);
    mockResult.setPromptTokens(100);
    mockResult.setCompletionTokens(200);
    return mockResult;
  }

  @Override
  public TenderExtractionResult combineTenderFields(
      List<TenderExtractionResult> tenderInformation) {
    TenderExtractionResult mockResult = new TenderExtractionResult();
    mockResult.setClient("Mock Client Organization");
    mockResult.setSubmissionDate("2024-12-31T23:59:59");
    mockResult.setBindingDeadline("30 days");
    mockResult.setContractDuration("12 months");
    mockResult.setPublicationDate("2024-01-01");
    mockResult.setQuestionDeadline("2024-12-15T17:00:00");
    mockResult.setContractValue(100000.0f);
    mockResult.setMaximumBudget("150 person-days");
    mockResult.setWinningCriteria("Best value for money");
    mockResult.setWeightingPriceQuality("70% quality, 30% price");
    mockResult.setDeliveryLocation("Remote/On-site hybrid");
    return mockResult;
  }

  @Override
  public com.gofore.aita.analysis.models.AnalysisResult combineAnalysisResults(
      List<com.gofore.aita.analysis.models.AnalysisResult> analysisResults) {
    com.gofore.aita.analysis.models.AnalysisResult mockResult =
        new com.gofore.aita.analysis.models.AnalysisResult();
    mockResult.setAnalysisResult("Mock combined analysis result for testing");
    mockResult.setPromptTokens(100);
    mockResult.setCompletionTokens(200);
    return mockResult;
  }
}
