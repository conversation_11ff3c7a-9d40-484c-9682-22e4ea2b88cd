package com.gofore.aita.mocks;

import com.azure.ai.openai.OpenAIAsyncClient;
import com.azure.ai.openai.OpenAIClient;
import com.azure.core.credential.TokenCredential;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * Test configuration for Azure services. Provides mock implementations of Azure services for
 * testing.
 */
@TestConfiguration
public class TestAzureConfig {

  @Bean
  public TokenCredential tokenCredential() {
    return Mockito.mock(TokenCredential.class);
  }

  @Bean
  public BlobServiceClientBuilder blobServiceClientBuilder() {
    BlobServiceClientBuilder builder = Mockito.mock(BlobServiceClientBuilder.class);
    Mockito.when(builder.credential(Mockito.any(TokenCredential.class))).then<PERSON><PERSON><PERSON>(builder);
    Mockito.when(builder.endpoint(Mockito.anyString())).thenR<PERSON>urn(builder);
    Mockito.when(builder.buildClient()).thenReturn(Mockito.mock(BlobServiceClient.class));
    return builder;
  }

  @Bean
  public OpenAIClient openAIClient() {
    return Mockito.mock(OpenAIClient.class);
  }

  @Bean
  @Primary
  public OpenAIAsyncClient openAIAsyncClient() {
    return Mockito.mock(OpenAIAsyncClient.class);
  }
}
