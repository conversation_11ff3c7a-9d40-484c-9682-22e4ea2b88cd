package com.gofore.aita.mocks;

import com.gofore.aita.tender.data.IFileStore;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

/**
 * Mock implementation of StorageAccountFileStore for testing. This implementation stores files in
 * memory and doesn't require any Azure resources.
 */
@Component
@Primary
@Profile("test")
public class MockStorageAccountFileStore implements IFileStore {

  private final Map<String, byte[]> fileContents = new HashMap<>();

  /**
   * Stores a file in the mock storage system.
   *
   * @param folderName Name of the folder where file should be stored
   * @param fileName Name of the file
   * @param fileStream Stream representing the file
   * @param fileSizeBytes File size in Bytes
   * @return A pair containing the file name and file path
   */
  @Override
  public Pair<String, String> storeFile(
      String folderName, String fileName, InputStream fileStream, long fileSizeBytes) {
    String filePath = folderName + "/" + fileName;

    try {
      // Read the input stream into a byte array
      ByteArrayOutputStream buffer = new ByteArrayOutputStream();
      int nRead;
      byte[] data = new byte[1024];
      while ((nRead = fileStream.read(data, 0, data.length)) != -1) {
        buffer.write(data, 0, nRead);
      }
      buffer.flush();

      // Store the file content
      fileContents.put(filePath, buffer.toByteArray());

      return Pair.of(fileName, filePath);
    } catch (IOException e) {
      throw new RuntimeException("Failed to store file in mock storage", e);
    }
  }

  /**
   * Deletes a file from the mock storage.
   *
   * @param filePath Full path to file
   */
  @Override
  public void deleteFile(String filePath) {
    fileContents.remove(filePath);
  }

  /**
   * Retrieves a file from the mock storage.
   *
   * @param filePath Path to file
   * @return InputStream that allows streaming the file, or null if file doesn't exist
   */
  @Override
  public InputStream retrieveFile(String filePath) {
    byte[] content = fileContents.get(filePath);
    return content != null ? new ByteArrayInputStream(content) : null;
  }

  /**
   * Checks if a file exists in the mock storage.
   *
   * @param filePath Path to check
   * @return true if the file exists, false otherwise
   */
  public boolean fileExists(String filePath) {
    return fileContents.containsKey(filePath);
  }

  /**
   * Gets the number of files currently stored in the mock storage.
   *
   * @return the number of files
   */
  public int getFileCount() {
    return fileContents.size();
  }

  /** Clears all files from the mock storage. */
  public void clearAll() {
    fileContents.clear();
  }
}
