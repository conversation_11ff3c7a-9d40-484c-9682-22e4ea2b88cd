package com.gofore.aita.utils;

import com.gofore.aita.tender.api.util.CredentialHelper;
import java.util.UUID;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * Utility class for common test operations. This class provides helper methods for creating test
 * data and common test objects.
 */
public class TestUtils {

  /**
   * Creates HTTP headers with authentication information for testing.
   *
   * @return HttpHeaders with authentication information
   */
  public static HttpHeaders createTestHttpHeaders() {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_ID, UUID.randomUUID().toString());
    httpHeaders.set(CredentialHelper.AUTH_HEADER_CLIENT_PRINCIPAL_NAME, "<EMAIL>");
    return httpHeaders;
  }

  /**
   * Creates a MultiValueMap with tender parameters for testing.
   *
   * @return MultiValueMap with tender parameters
   */
  public static MultiValueMap<String, String> createTenderParams() {
    MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
    params.add("title", "Test Tender");
    params.add("description", "Test Description");
    params.add("sourceUrl", "http://example.com/tender");
    params.add("client", "Test Client");
    params.add("submissionDate", "2025-01-01T12:00:00Z");
    params.add("bindingDeadline", "2025-02-01");
    params.add("contractDuration", "12 months");
    params.add("publicationDate", "2024-12-01");
    params.add("questionDeadline", "2024-12-15T12:00:00Z");
    params.add("winningCriteria", "Best value");
    params.add("weightingPriceQuality", "50/50");
    params.add("deliveryLocation", "Test Location");
    return params;
  }

  /**
   * Creates a mock multipart file for testing.
   *
   * @param fileName Name of the file
   * @param content Content of the file
   * @return MockMultipartFile for testing
   */
  public static MockMultipartFile createMockFile(String fileName, String content) {
    return new MockMultipartFile("files", fileName, "text/plain", content.getBytes());
  }
}
