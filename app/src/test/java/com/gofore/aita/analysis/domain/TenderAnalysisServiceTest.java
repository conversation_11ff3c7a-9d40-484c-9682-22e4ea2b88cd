package com.gofore.aita.analysis.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.gofore.aita.analysis.api.IAnalysisService;
import com.gofore.aita.analysis.models.AnalysisResult;
import com.gofore.aita.core.domain.SystemConfigurationService;
import com.gofore.aita.core.domain.models.SystemConfiguration;
import com.gofore.aita.extraction.domain.FileExtractionService;
import com.gofore.aita.extraction.domain.TextProcessingService;
import com.gofore.aita.extraction.domain.TextProcessingService.TextWithTokenCount;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.extraction.models.ExtractionStatus;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.exceptions.ResourceNotFoundException;
import com.gofore.aita.tender.domain.mapper.AnalysisResultMapper;
import com.gofore.aita.tender.domain.models.FileMetadata;
import com.gofore.aita.tender.domain.models.Tender;
import java.util.Collections;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TenderAnalysisServiceTest {

  @Mock private TenderRepository tenderRepository;
  @Mock private FileExtractionService fileExtractionService;
  @Mock private TextProcessingService textProcessingService;
  @Mock private SystemConfigurationService systemConfigurationService;
  @Mock private IAnalysisService analysisService;
  @Mock private AnalysisResultMapper analysisResultMapper;

  private TenderAnalysisService service;
  private Tender tender;
  private ExtractedDocument extractedDocument;
  private TextWithTokenCount combinedText;
  private SystemConfiguration systemConfiguration;
  private AnalysisResult analysisResult;
  private com.gofore.aita.tender.domain.models.AnalysisResult mappedResult;

  @BeforeEach
  void setUp() {
    service =
        new TenderAnalysisService(
            tenderRepository,
            fileExtractionService,
            textProcessingService,
            systemConfigurationService,
            analysisService,
            analysisResultMapper);

    tender = new Tender();
    tender.setId("test-tender-id");
    tender.setDescription("Test tender description");

    FileMetadata fileMetadata = new FileMetadata();
    fileMetadata.setId("test-file-id");
    fileMetadata.setFileName("test-file.pdf");
    tender.setFiles(Collections.singletonList(fileMetadata));

    extractedDocument = new ExtractedDocument();
    extractedDocument.setFileId("test-file-id");
    extractedDocument.setText("Extracted text from test file");
    extractedDocument.setTokenCount(10);
    extractedDocument.setStatus(ExtractionStatus.SUCCESS);

    combinedText = new TextWithTokenCount("Combined text for analysis", 20);

    systemConfiguration = new SystemConfiguration();
    systemConfiguration.setAiAnalysisAnalysisPrompt("Test analysis prompt");
    systemConfiguration.setAiStructuredOutputPrompt("Test structured output prompt");

    analysisResult = new AnalysisResult();
    analysisResult.setAnalysisResult("Test analysis result");
    analysisResult.setPromptTokens(5);
    analysisResult.setCompletionTokens(15);

    mappedResult = new com.gofore.aita.tender.domain.models.AnalysisResult();
    mappedResult.setAnalysisResult("Test analysis result");
    mappedResult.setPromptTokens(5);
    mappedResult.setCompletionTokens(15);
  }

  @Test
  void analyzeTender_ShouldProcessFilesAndAnalyzeText() {
    // Arrange
    when(tenderRepository.findById(tender.getId())).thenReturn(Optional.of(tender));
    when(tenderRepository.save(tender)).thenReturn(tender);
    when(fileExtractionService.processFilesForExtraction(any(Tender.class)))
        .thenReturn(Collections.singletonList(extractedDocument));
    when(textProcessingService.combineTexts(
            tender.getDescription(), Collections.singletonList(extractedDocument)))
        .thenReturn(combinedText);
    when(systemConfigurationService.get()).thenReturn(systemConfiguration);
    when(analysisService.analyzeTender(
            combinedText.getText(), systemConfiguration.getAiAnalysisAnalysisPrompt()))
        .thenReturn(analysisResult);
    when(analysisResultMapper.map(analysisResult)).thenReturn(mappedResult);

    // Act
    com.gofore.aita.tender.domain.models.AnalysisResult result =
        service.analyzeTender(tender.getId());

    // Assert
    assertNotNull(result);
    assertEquals(mappedResult, result);

    verify(tenderRepository).findById(tender.getId());
    verify(fileExtractionService).processFilesForExtraction(tender);
    verify(textProcessingService)
        .combineTexts(tender.getDescription(), Collections.singletonList(extractedDocument));
    verify(systemConfigurationService).get();
    verify(analysisService)
        .analyzeTender(combinedText.getText(), systemConfiguration.getAiAnalysisAnalysisPrompt());
    verify(analysisResultMapper).map(analysisResult);
    verify(tenderRepository, times(2)).save(tender);
  }

  @Test
  void analyzeTender_ShouldHandleEmptyExtractedDocuments() {
    // Arrange
    when(tenderRepository.findById(tender.getId())).thenReturn(Optional.of(tender));
    when(tenderRepository.save(tender)).thenReturn(tender);
    when(fileExtractionService.processFilesForExtraction(tender))
        .thenReturn(Collections.emptyList());
    when(textProcessingService.combineTexts(tender.getDescription(), Collections.emptyList()))
        .thenReturn(combinedText);
    when(systemConfigurationService.get()).thenReturn(systemConfiguration);
    when(analysisService.analyzeTender(
            combinedText.getText(), systemConfiguration.getAiAnalysisAnalysisPrompt()))
        .thenReturn(analysisResult);
    when(analysisResultMapper.map(analysisResult)).thenReturn(mappedResult);

    // Act
    com.gofore.aita.tender.domain.models.AnalysisResult result =
        service.analyzeTender(tender.getId());

    // Assert
    assertNotNull(result);
    assertEquals(mappedResult, result);

    verify(tenderRepository).findById(tender.getId());
    verify(fileExtractionService).processFilesForExtraction(tender);
    verify(textProcessingService).combineTexts(tender.getDescription(), Collections.emptyList());
  }

  @Test
  void analyzeTender_ShouldThrowResourceNotFoundException_WhenTenderNotFound() {
    // Arrange
    when(tenderRepository.findById("non-existent-id")).thenReturn(Optional.empty());

    // Act & Assert
    assertThrows(ResourceNotFoundException.class, () -> service.analyzeTender("non-existent-id"));
  }
}
