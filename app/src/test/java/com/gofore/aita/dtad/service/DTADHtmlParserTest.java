package com.gofore.aita.dtad.service;

import com.gofore.aita.dtad.model.DTADTender;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class DTADHtmlParserTest {

  private DTADHtmlParser htmlParser;

  @BeforeEach
  void setUp() {
    htmlParser = new DTADHtmlParser();
  }

  @Test
  void testParseSearchResults_EmptyHtml() {
    // Test with null HTML
    List<DTADTender> result = htmlParser.parseSearchResults(null, "test-profile");
    assertTrue(result.isEmpty());

    // Test with empty HTML
    result = htmlParser.parseSearchResults("", "test-profile");
    assertTrue(result.isEmpty());

    // Test with whitespace HTML
    result = htmlParser.parseSearchResults("   ", "test-profile");
    assertTrue(result.isEmpty());
  }

  @Test
  void testParseSearchResults_NoTenderRows() {
    String htmlWithoutTenders = """
        <html>
        <body>
          <div class="content">
            <p>No tenders found</p>
          </div>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(htmlWithoutTenders, "test-profile");
    assertTrue(result.isEmpty());
  }

  @Test
  void testParseSearchResults_BasicTenderRow() {
    String htmlWithBasicTender = """
        <html>
        <body>
          <table class="rfqTableList">
            <tr class="rfqRow" id="rfq12345">
              <td class="list-column1">
                <input name="rfqIds" value="12345_1" type="checkbox">
              </td>
              <td class="list-column1">
                <span class="listLink">Test Tender Title</span>
              </td>
              <td class="list-column1">20459 Hamburg</td>
              <td class="list-column1">04.09.2025</td>
              <td class="list-column1">30.09.2025</td>
            </tr>
            <tr class="short_desc_12345_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>DTAD-ID:</b></td>
              <td>12345</td>
              <td></td>
            </tr>
          </table>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(htmlWithBasicTender, "test-profile");
    
    assertEquals(1, result.size());
    
    DTADTender tender = result.get(0);
    assertEquals("12345", tender.getDtadId());
    assertEquals("Test Tender Title", tender.getTitle());
    assertEquals("20459 Hamburg", tender.getRegion());
    assertEquals("04.09.2025", tender.getDatum());
    assertEquals("30.09.2025", tender.getFrist());
    assertEquals("test-profile", tender.getProfileId());
    assertEquals("DTAD", tender.getExtractionSource());
    assertNotNull(tender.getExtractedAt());
  }

  @Test
  void testParseSearchResults_CompleteRealExample() {
    String realExampleHtml = """
        <html>
        <body>
          <table class="rfqTableList">
            <tr class="rfqRow" id="rfq23439956">
              <td class="list-column1">
                <input name="rfqIds" value="23439956_1" type="checkbox">
              </td>
              <td class="list-column1">
                <span class="listLink">Bereitstellung und Betrieb eines virtuellen Windows Server</span>
              </td>
              <td class="list-column1">20459 Hamburg</td>
              <td class="list-column1">04.09.2025</td>
              <td class="list-column1">30.09.2025</td>
            </tr>
            <tr class="short_desc_23439956_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>DTAD-ID:</b></td>
              <td>23439956</td>
              <td></td>
            </tr>
            <tr class="short_desc_23439956_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>Auftragsart:</b></td>
              <td>Nationale Ausschreibung</td>
              <td>Frist Angebotsabgabe:</td>
              <td>30.09.2025</td>
            </tr>
            <tr class="short_desc_23439956_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>Dokumententyp:</b></td>
              <td>Ausschreibung</td>
              <td>Zeitvertrag endet:</td>
              <td>Dezember 2026</td>
            </tr>
            <tr class="short_desc_23439956_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>Beschreibung:</b></td>
              <td colspan="7">Bereitstellung und Betrieb eines virtuellen Windows Server\\<br>\\<br>Branchenspezifisches Softwarepaket</td>
            </tr>
            <tr class="short_desc_23439956_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>Vergabestelle:</b></td>
              <td colspan="7">Generalzolldirektion Zentrale Beschaffungsstelle der Bundesfinanzverwaltung</td>
            </tr>
            <tr class="short_desc_23439956_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>Erfüllungsort:</b></td>
              <td colspan="7">Generalzolldirektion, Referat DIII.B.2, Stubbenhuk 3, 20459 Hamburg</td>
            </tr>
          </table>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(realExampleHtml, "test-profile");
    
    assertEquals(1, result.size());
    
    DTADTender tender = result.get(0);
    assertEquals("23439956", tender.getDtadId());
    assertEquals("Bereitstellung und Betrieb eines virtuellen Windows Server", tender.getTitle());
    assertEquals("20459 Hamburg", tender.getRegion());
    assertEquals("04.09.2025", tender.getDatum());
    assertEquals("30.09.2025", tender.getFrist());
    assertEquals("Nationale Ausschreibung", tender.getAuftragsart());
    assertEquals("30.09.2025", tender.getFristAngebotsabgabe());
    assertEquals("Ausschreibung", tender.getDokumententyp());
    assertEquals("Dezember 2026", tender.getZeitvertragEndet());
    assertNotNull(tender.getBeschreibung());
    assertTrue(tender.getBeschreibung().contains("Bereitstellung und Betrieb"));
    assertEquals("Generalzolldirektion Zentrale Beschaffungsstelle der Bundesfinanzverwaltung", tender.getAuftraggeber());
    assertEquals("Generalzolldirektion, Referat DIII.B.2, Stubbenhuk 3, 20459 Hamburg", tender.getErfuellungsort());
  }

  @Test
  void testParseSearchResults_MultipleTenders() {
    String htmlWithMultipleTenders = """
        <html>
        <body>
          <table class="rfqTableList">
            <tr class="rfqRow" id="rfq12345">
              <td class="list-column1">
                <span class="listLink">First Tender</span>
              </td>
              <td class="list-column1">10115 Berlin</td>
              <td class="list-column1">01.09.2025</td>
              <td class="list-column1">15.09.2025</td>
            </tr>
            <tr class="short_desc_12345_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>DTAD-ID:</b></td>
              <td>12345</td>
            </tr>
            <tr class="rfqRow" id="rfq67890">
              <td class="list-column1">
                <span class="listLink">Second Tender</span>
              </td>
              <td class="list-column1">80331 München</td>
              <td class="list-column1">02.09.2025</td>
              <td class="list-column1">16.09.2025</td>
            </tr>
            <tr class="short_desc_67890_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>DTAD-ID:</b></td>
              <td>67890</td>
            </tr>
          </table>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(htmlWithMultipleTenders, "test-profile");

    assertEquals(2, result.size());

    // Sort results by DTAD ID to ensure consistent order (handle null IDs)
    result.sort((a, b) -> {
      String idA = a.getDtadId() != null ? a.getDtadId() : "";
      String idB = b.getDtadId() != null ? b.getDtadId() : "";
      return idA.compareTo(idB);
    });

    // Check first tender (12345)
    DTADTender first = result.get(0);
    assertEquals("12345", first.getDtadId());
    assertEquals("First Tender", first.getTitle());
    assertEquals("10115 Berlin", first.getRegion());

    // Check second tender (67890)
    DTADTender second = result.get(1);
    assertEquals("67890", second.getDtadId());
    assertEquals("Second Tender", second.getTitle());
    assertEquals("80331 München", second.getRegion());
  }

  @Test
  void testParseSearchResults_NoRfqTableList() {
    String htmlWithoutTable = """
        <html>
        <body>
          <table>
            <tr class="rfqRow" id="rfq12345">
              <td class="list-column1">
                <span class="listLink">Direct Tender</span>
              </td>
              <td class="list-column1">10115 Berlin</td>
              <td class="list-column1">01.09.2025</td>
              <td class="list-column1">15.09.2025</td>
            </tr>
            <tr class="short_desc_12345_1 rfqDesc">
              <td></td>
              <td></td>
              <td><b>DTAD-ID:</b></td>
              <td>12345</td>
            </tr>
          </table>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(htmlWithoutTable, "test-profile");

    assertEquals(1, result.size());
    DTADTender tender = result.get(0);
    assertEquals("Direct Tender", tender.getTitle());
    assertEquals("12345", tender.getDtadId());
    assertEquals("10115 Berlin", tender.getRegion());
  }
}
