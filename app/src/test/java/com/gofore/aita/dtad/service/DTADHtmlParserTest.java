package com.gofore.aita.dtad.service;

import static org.junit.jupiter.api.Assertions.*;

import com.gofore.aita.dtad.model.DTADTender;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DTADHtmlParserTest {

  private DTADHtmlParser htmlParser;

  @BeforeEach
  void setUp() {
    htmlParser = new DTADHtmlParser();
  }

  @Test
  void testParseSearchResults_EmptyHtml() {
    // Test with null HTML
    List<DTADTender> result = htmlParser.parseSearchResults(null, "test-profile");
    assertTrue(result.isEmpty());

    // Test with empty HTML
    result = htmlParser.parseSearchResults("", "test-profile");
    assertTrue(result.isEmpty());

    // Test with whitespace HTML
    result = htmlParser.parseSearchResults("   ", "test-profile");
    assertTrue(result.isEmpty());
  }

  @Test
  void testParseSearchResults_NoTenderRows() {
    String htmlWithoutTenders =
        """
        <html>
        <body>
          <div class="content">
            <p>No tenders found</p>
          </div>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(htmlWithoutTenders, "test-profile");
    assertTrue(result.isEmpty());
  }

  @Test
  void testParseSearchResults_BasicTenderRow() {
    String htmlWithBasicTender =
        """
        <html>
        <body>
          <table>
            <tr class="rfqRow" id="rfq12345">
              <td>
                <input name="rfqIds" value="12345_1" type="checkbox">
              </td>
              <td>
                <span class="listLink" title="Test Tender Description">Test Tender Title</span>
              </td>
              <td>
                <img alt="Nationale Ausschreibung" src="...">
              </td>
            </tr>
          </table>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(htmlWithBasicTender, "test-profile");

    assertEquals(1, result.size());

    DTADTender tender = result.get(0);
    assertEquals("12345_1", tender.getTenderId());
    assertEquals("12345", tender.getTenderNumber());
    assertEquals("Test Tender Title", tender.getTitle());
    assertEquals("Test Tender Description", tender.getFullDescription());
    assertEquals("Test Tender Title", tender.getShortDescription());
    assertEquals("Nationale Ausschreibung", tender.getTenderType());
    assertEquals("test-profile", tender.getProfileId());
    assertEquals("DTAD", tender.getExtractionSource());
    assertNotNull(tender.getExtractedAt());
  }

  @Test
  void testParseSearchResults_MultipleTenders() {
    String htmlWithMultipleTenders =
        """
        <html>
        <body>
          <table>
            <tr class="rfqRow" id="rfq12345">
              <td>
                <input name="rfqIds" value="12345_1" type="checkbox">
              </td>
              <td>
                <span class="listLink" title="First Tender">First Title</span>
              </td>
            </tr>
            <tr class="rfqRow" id="rfq67890">
              <td>
                <input name="rfqIds" value="67890_1" type="checkbox">
              </td>
              <td>
                <span class="listLink" title="Second Tender">Second Title</span>
              </td>
            </tr>
          </table>
        </body>
        </html>
        """;

    List<DTADTender> result =
        htmlParser.parseSearchResults(htmlWithMultipleTenders, "test-profile");

    assertEquals(2, result.size());

    // Check first tender
    DTADTender first = result.get(0);
    assertEquals("12345_1", first.getTenderId());
    assertEquals("First Title", first.getTitle());

    // Check second tender
    DTADTender second = result.get(1);
    assertEquals("67890_1", second.getTenderId());
    assertEquals("Second Title", second.getTitle());
  }

  @Test
  void testParseSearchResults_InvalidTenderRow() {
    String htmlWithInvalidTender =
        """
        <html>
        <body>
          <table>
            <tr class="rfqRow" id="rfq12345">
              <!-- Missing required elements -->
            </tr>
          </table>
        </body>
        </html>
        """;

    List<DTADTender> result = htmlParser.parseSearchResults(htmlWithInvalidTender, "test-profile");

    // Should skip invalid tender rows
    assertTrue(result.isEmpty());
  }

  @Test
  void testParseSearchResults_MalformedHtml() {
    String malformedHtml =
        """
        <html>
        <body>
          <table>
            <tr class="rfqRow" id="rfq12345">
              <td>
                <span class="listLink" title="Test">Title</span>
              <!-- Missing closing tags
        """;

    // Should handle malformed HTML gracefully
    List<DTADTender> result = htmlParser.parseSearchResults(malformedHtml, "test-profile");

    // JSoup should parse this and we might get some results
    assertNotNull(result);
  }
}
