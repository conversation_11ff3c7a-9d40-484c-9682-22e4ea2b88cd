package com.gofore.aita;

import com.azure.ai.openai.OpenAIAsyncClient;
import com.azure.ai.openai.OpenAIClient;
import com.azure.core.credential.TokenCredential;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gofore.aita.mocks.MockStorageAccountFileStore;
import com.gofore.aita.mocks.TestAzureConfig;
import com.gofore.aita.tender.data.IFileStore;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

/** Base class for all tests. Provides common setup and utilities. */
@SpringBootTest(classes = {TestAzureConfig.class, Application.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
public abstract class BaseTest {

  // ─────────────────────────────────────────────────────────────
  // STATIC BLOCK — runs as soon as this class is loaded,
  // before any Azure SDK classes ever see the light of day:
  // ─────────────────────────────────────────────────────────────
  static {
    System.setProperty("AZURE_IDENTITY_DISABLE_MANAGED_IDENTITY_CREDENTIAL", "true");
    System.setProperty("AZURE_IDENTITY_DISABLE_IMDS_ENDPOINT", "true");
    System.setProperty("AZURE_IDENTITY_DISABLE_ENVIRONMENT_CREDENTIAL", "true");
    System.setProperty("AZURE_IDENTITY_DISABLE_AZURE_CLI_CREDENTIAL", "true");
    System.setProperty("AZURE_IDENTITY_DISABLE_VISUAL_STUDIO_CODE_CREDENTIAL", "true");
    System.setProperty("AZURE_IDENTITY_DISABLE_INTELLIJ_CREDENTIAL", "true");
    System.setProperty("AZURE_IDENTITY_DISABLE_SHARED_TOKEN_CACHE_CREDENTIAL", "true");
    System.setProperty("AZURE_IDENTITY_DISABLE_DEVELOPER_CREDENTIAL", "true");
    System.setProperty("AZURE_IDENTITY_TIMEOUT_IN_MS", "1");
    System.setProperty("AZURE_CORE_HTTP_CONNECT_TIMEOUT", "1");
    System.setProperty("AZURE_CORE_HTTP_READ_TIMEOUT", "1");
    System.setProperty("AZURE_TELEMETRY_DISABLED", "true");
  }

  @Autowired protected MockMvc mvc;
  @Autowired protected IFileStore fileStore;
  protected final ObjectMapper objectMapper = new ObjectMapper();

  // these replace the real Azure beans and MongoDB
  @MockitoBean private TokenCredential tokenCredential;
  @MockitoBean private BlobServiceClientBuilder blobServiceClientBuilder;
  @MockitoBean private OpenAIClient openAIClient;
  @MockitoBean private OpenAIAsyncClient openAIAsyncClient;

  @BeforeEach
  public void setUp() {
    // stub out the fluent BlobServiceClientBuilder
    Mockito.when(blobServiceClientBuilder.credential(Mockito.any(TokenCredential.class)))
        .thenReturn(blobServiceClientBuilder);
    Mockito.when(blobServiceClientBuilder.endpoint(Mockito.anyString()))
        .thenReturn(blobServiceClientBuilder);
    Mockito.when(blobServiceClientBuilder.buildClient())
        .thenReturn(Mockito.mock(BlobServiceClient.class));

    // clear in-memory file store between tests
    if (fileStore instanceof MockStorageAccountFileStore) {
      ((MockStorageAccountFileStore) fileStore).clearAll();
    }
  }
}
