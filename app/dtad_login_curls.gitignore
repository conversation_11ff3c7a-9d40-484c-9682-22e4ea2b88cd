curl 'https://www.dtad.de/workxl/myAccount/login/validate.do' \
  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'cache-control: max-age=0' \
  -H 'content-type: application/x-www-form-urlencoded' \
  -b 'usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18' \
  -H 'origin: https://www.dtad.com' \
  -H 'priority: u=0, i' \
  -H 'referer: https://www.dtad.com/de/' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: document' \
  -H 'sec-fetch-mode: navigate' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-user: ?1' \
  -H 'upgrade-insecure-requests: 1' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw 'use360View=1&username=accountname&password=accountpassword&saveLogin=1' ;
curl $'data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'14\' height=\'11\' viewBox=\'0 0 14 11\'%3E%3Cpath fill=\'%23fff\' d=\'M12 1 5 7.5 2 5l-1 .5L5 10l8-8.5z\'/%3E%3C/svg%3E' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'https://track-eu1.hubspot.com/__ptc.gif?_hs_tag_name=BUTTON&_hs_element_id=&_hs_element_class=A-link++uk-width-1-1+uk-width-auto%40s+uk-button+uk-button-primary+uk-button-null+uk-button-large+%40fs--button_l+M-buttons__button++&_hs_element_text=Anmelden&_hs_element_visible=&_hs_link_href=&_hs_parent_module_id=module_header&_hs_viewport_width=1728&_hs_viewport_height=430&_hs_document_width=1728&_hs_document_height=7079&_hs_screen_width=1728&_hs_screen_height=1117&_hs_scroll_x_coordinate=&_hs_scroll_y_coordinate=&_hs_mouse_x_coordinate=922&_hs_mouse_y_coordinate=304&_hs_offset_x=144&_hs_offset_y=24&_hs_key_ctrl=&_hs_key_shift=&_hs_key_alt=&_hs_key_meta=&_hs_touch_screen=&_hs_selector=div%23hs_cos_wrapper_module_header.hs_cos_wrapper.hs_cos_wrapper_widget.hs_cos_wrapper_type_module+%3E+header.MO-header.uk-position-fixed.uk-width-1-1.uk-position-z-index+%3E+div.uk-container.MO-header__content+%3E+div.uk-flex.uk-flex-right+%3E+div.uk-dropbar.uk-drop.MO-header__nav.uk-flex-1+%3E+div.MO-header__dropContent.uk-flex-middle.uk-flex-1.uk-flex-between+%3E+div.MO-header__buttons.MO-header__landing+%3E+div.uk-drop.uk-width-large.MO-header__drop+%3E+div.H-form.MO-form--nobg+%3E+form%5Bclass*%3D%22hs-form%22%5D+%3E+button.A-link.uk-width-1-1.uk-width-auto%40s.uk-button.uk-button-primary.uk-button-null.uk-button-large.%40fs--button_l.M-buttons__button&_hs_is_navigation=true&sd=1728x1117&cd=30-bit&cs=UTF-8&ln=de-de&bfp=2224341508&v=1.1&a=3485369&pi=61374564205&ct=standard-page&ccu=https%3A%2F%2Fwww.dtad.com%2Fde%2F&cpi=61374564205&lpi=61374564205&abi=75159941417&lvi=61374564205&lvc=de&pu=https%3A%2F%2Fwww.dtad.com%2Fde%2F&t=DTAD+Plattform+%E2%80%93+Aktuelle+Ausschreibungen+%26+Marktdaten+in+einem+System&cts=1756988503322&vi=cf5f0627f3390a55d600ed554d85e35d&nc=false&u=96424963.cf5f0627f3390a55d600ed554d85e35d.1756889760856.1756889760856.1756988466770.2&b=96424963.1.1756988466770&cc=15' \
  -H 'accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b '_cfuvid=Ib1YcqfGKFAzEMdd4ouUdJatbsiyUioPWuVt1G9ZHDE-1756718019252-*******-604800000; __cf_bm=4JAB7mLQUHmxeWYz824naW54NxX7cVKNS4987R9cxrw-1756988466-*******-bDyBgBPwZ8Alt.QWVb50fBoaRCAXaKp8eWnJZvsPgjj9j7uHxPhRVK7jUP6FzfWhqe6ipLyOxV1Wf0XqphtkdAm.qRA_CX0Oplg.1cKk6xE' \
  -H 'priority: i' \
  -H 'referer: https://www.dtad.com/de/' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: image' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'chrome-extension://oboonakemofpalcgghocfoadofidjkkk/css/colors.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'chrome-extension://oboonakemofpalcgghocfoadofidjkkk/css/banner.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'chrome-extension://oboonakemofpalcgghocfoadofidjkkk/css/button.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'https://www.dtad.de/workxl/layout/styles/cxl_css-************.css' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/styles/fuv-************.css' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/prototype-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/scriptaculous/scriptaculous.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/wxl_basic-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/wxl_360-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/pageOverlay-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/nlstree-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/overlayController.do?nocache=*************' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: script' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/js/wxl_typo_help-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/calendarview-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/usersnap-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/swiper.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/sortable.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://maps.googleapis.com/maps/api/js?libraries=visualization&v=quarterly&key=AIzaSyCbe24EbirCo92vAX0XPJ5e4uZE0jREk7A' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/styles/cxl_print360-************.css' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/listener/dtad360customEvents-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/highcharts/adapters/prototype-adapter-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/highcharts/highcharts_4.2.4-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/highcharts/highcharts-more_4.2.4-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/highcharts/modules/funnel-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/highcharts/modules/no_data_to_display-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/highcharts/modules/solid-gauge-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/fileUploadDragDrop.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/main-************.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/kreuz.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/bird_bg_medium.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/searching.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/scriptaculous/effects.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/scriptaculous/controls.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/js/scriptaculous/dragdrop.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fonts.googleapis.com/css?family=Open+Sans:300,400,600' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fonts.gstatic.com/s/opensans/v43/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-mu0SC55I.woff2' \
  -H 'Origin: https://www.dtad.de' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://fonts.googleapis.com/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/arr_trans_left.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/arr_trans_right.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015' \
  -H 'Origin: https://www.dtad.de' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.googletagmanager.com/gtm.js?id=GTM-MRZJ7V' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://maps.googleapis.com/maps/api/mapsjs/gen_204?csp_test=true' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-browser-channel: stable' \
  -H 'x-browser-copyright: Copyright 2025 Google LLC. All rights reserved.' \
  -H 'x-browser-validation: Hg4L+ikvx4e+Kz4C1Vi1rALvggw=' \
  -H 'x-browser-year: 2025' \
  -H 'x-client-data: CKuNywE=' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/crm/icon_list_add.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=404&_cb=*************&body=JSON' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: 590c63c8-3c54-411d-adf0-f2cc9c8665a4' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/dtad_ladegrafik_animation_a_02.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/bg_head_l.jpg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/cxl_css-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/dtad_nextgen_logo.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/sprite_main_navi.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/dtad_360_icons_flyouts.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/icon_megafon.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/dtad_360_icons_ribbons_14.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://consent.cookiebot.com/uc.js?cbid=********-783f-432b-88e9-1d3698ef8bd4' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/sprite_icons_crm.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/cal_step_before.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/cal_step_next.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://consentcdn.cookiebot.com/sdk/bc-v4.min.html' \
  -H 'Upgrade-Insecure-Requests: 1' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' ;
curl 'https://www.usetiful.com/dist/usetiful.js' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'if-modified-since: Wed, 03 Sep 2025 07:38:24 GMT' \
  -H 'if-none-match: W/"68b7f070-29c1b"' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: script' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=401&_cb=*************&body=JSON&workflowId=3314' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: c3bf846d-6aa6-499e-a646-e4d4bc11ebe4' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=424&_cb=*************&body=JSON&workflowId=3314' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: 7b882a9b-66ac-4bc1-9206-2d478858251d' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-data-proxy?crm_data_action=1&body=JSON&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/myAccount/newsItems.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' \
  --data-raw 'inline=1' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=404&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.usetiful.com/dist/u-content-loader.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-smart-tips.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-banners.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-tours.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/api-space/data.json?lang=de' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json; charset=utf-8' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-auth-token: 4d714aabd21c9a016cfde2c0d65a65a2' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/myAccount/rfq/contentTaskList.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' \
  --data-raw 'sidebar=1&from=01.08.2025&to=01.11.2025&selectedDate=04.09.2025' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=444&_cb=*************&body=JSON&workflowStepId=20154' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: 8aa335c1-468e-46cf-aaee-3cf609a9bbd0' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=444&_cb=*************&body=JSON&workflowStepId=20155' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: 816718ba-e25d-49df-9c57-0befc42f6950' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=444&_cb=*************&body=JSON&workflowStepId=20156' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: bf182afe-10e9-4bc5-877d-8a7775d59853' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=444&_cb=*************&body=JSON&workflowStepId=20157' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: b3db4c9e-0fd8-4648-9b9a-9ec6ce33ef41' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=444&_cb=*************&body=JSON&workflowStepId=20158' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: bbd0d59f-2df4-408d-9b49-c5f2a3d6a509' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=444&_cb=*************&body=JSON&workflowStepId=20159' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: a87cd8cb-1a2b-4d52-8cc4-f14879880450' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/letter.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/sprite_icons_module_right.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.com/hubfs/favicon/site.webmanifest' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'chrome-extension://nhdogjmejiglipccpnnnanhbledajbpd/dist/detector.js' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?_cb=*************&body=JSON&crm_api_action=112&assignedAccountId=**********' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: ec0a5a86-e9b6-476d-8981-547c01305bea' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/cdn-cgi/rum?' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json' \
  -b 'widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"resources":[],"referrer":"https://www.dtad.com/de/","eventType":1,"firstPaint":512,"firstContentfulPaint":512,"startTime":*************.1,"versions":{"fl":"2025.8.0","js":"2024.6.1","timings":1},"pageloadId":"8bd3c438-7680-4fdd-945f-dbc7df959bdd","location":"https://www.dtad.de/workxl/myAccount/login/validate.do","nt":"navigate","serverTimings":[{"name":"cfCacheStatus","dur":0,"desc":"DYNAMIC"},{"name":"cfOrigin","dur":207,"desc":""},{"name":"cfEdge","dur":22,"desc":""}],"timingsV2":{"nextHopProtocol":"h2","transferSize":26396,"decodedBodySize":120858},"dt":"","siteToken":"1255668450d944d29cfa0f87ba8b377b","st":2}' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://imgsct.cookiebot.com/1.gif?dgi=********-783f-432b-88e9-1d3698ef8bd4' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.zdassets.com/ekr/snippet.js?key=e39efb44-996f-45b7-9240-9cdd83edde72' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/cdn-cgi/rum?' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json' \
  -b 'widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"resources":[],"referrer":"https://www.dtad.com/de/","eventType":1,"firstPaint":512,"firstContentfulPaint":512,"startTime":*************.1,"versions":{"fl":"2025.8.0","js":"2024.6.1","timings":1},"pageloadId":"7507ddbd-ec5e-4a77-879a-d92404d42cf6","location":"https://www.dtad.de/workxl/myAccount/index.do","nt":"navigate","serverTimings":[{"name":"cfCacheStatus","dur":0,"desc":"DYNAMIC"},{"name":"cfOrigin","dur":207,"desc":""},{"name":"cfEdge","dur":22,"desc":""}],"timingsV2":{"nextHopProtocol":"h2","transferSize":26396,"decodedBodySize":120858},"dt":"","siteToken":"1255668450d944d29cfa0f87ba8b377b","st":2}' ;
curl 'https://consentcdn.cookiebot.com/consentconfig/********-783f-432b-88e9-1d3698ef8bd4/state.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://ekr.zdassets.com/compose/e39efb44-996f-45b7-9240-9cdd83edde72' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.zdassets.com/web_widget/messenger/latest/web-widget-main-cd0b9d8.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-devkit.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-forms.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-checklists.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-assistants.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.usetiful.com/dist/u-surveys.js?v=2025.09.03.073413-gca86cf2f20d-38' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'chrome-extension://oboonakemofpalcgghocfoadofidjkkk/css/colors.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'chrome-extension://oboonakemofpalcgghocfoadofidjkkk/css/banner.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'chrome-extension://oboonakemofpalcgghocfoadofidjkkk/css/button.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'chrome-extension://oboonakemofpalcgghocfoadofidjkkk/icons/keepassxc.svg' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'Referer;' ;
curl 'https://static.zdassets.com/web_widget/messenger/latest/web-widget-locales/messenger/de-de-json-cd0b9d8.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.zdassets.com/web_widget/messenger/latest/web-widget-6322-cd0b9d8.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.zdassets.com/web_widget/messenger/latest/web-widget-7437-cd0b9d8.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.zdassets.com/web_widget/messenger/latest/web-widget-3190-cd0b9d8.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.zdassets.com/web_widget/messenger/latest/web-widget-8173-cd0b9d8.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://static.zdassets.com/web_widget/messenger/latest/web-widget-2229-cd0b9d8.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/myAccount/account/loadTabs.do' \
  -X 'POST' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://dtad.zendesk.com/frontendevents/pv?client=1B752747-577B-429A-A0E0-83861AF69088' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"url":null,"buid":"9b9414b32c74421a9232c7a908fb1e16","channel":"web_messenger","version":"cd0b9d8","timestamp":"2025-09-04T12:21:44.153Z","suid":"5d1af773461a4f328a6adf927a9b461d","pageView":{"pageTitle":"DTAD","referrer":null,"time":0,"loadTime":78,"navigatorLanguage":"de-DE","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","helpCenterDedup":false}}' ;
curl 'https://dtad.zendesk.com/embeddable/config' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://consentcdn.cookiebot.com/consentconfig/********-783f-432b-88e9-1d3698ef8bd4/settings.json' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://consent.cookiebot.com/Scripts/widgetIcon.min.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://dtad.zendesk.com/embeddable/config' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'if-modified-since: Thu, 04 Sep 2025 12:19:34 GMT' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=4, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://fonts.gstatic.com/s/opensans/v43/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTUGmu0SC55K5gw.woff2' \
  -H 'Origin: https://www.dtad.de' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://fonts.googleapis.com/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/myAccount/index360.do' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw 'get360Dashboard=true' ;
curl 'https://www.dtad.de/workxl/360/searchForm.do' \
  -X 'POST' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/360/dtad_360_icons_crm_08.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/myAccount/rfq/mostRecentSelectedList.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/sp_widget_01.svg' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/widget/widget_no_profileResult.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/widget/widget_no_userPerformanceMulti.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/widget/widget_no_userPerformance.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/widget/widget_no_profileList.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/widget/widget_no_appointments.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/widget/widget_no_lastViewed.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/widget/widget_no_assigned.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer: https://www.dtad.de/workxl/layout/styles/360-************.css' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://widget.usersnap.com/global/load/19e83c55-a5e1-4362-bd3b-6434de80210f?onload=onUsersnapCXLoad' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: script' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/calendar_blue.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/arrow_button_red.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=504&_cb=*************&body=JSON&assignedAccountId=**********' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'correlation_id: ea67e516-3376-4004-bc0d-c363b72da4e3' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://resources.usersnap.com/widget-assets/js/entries/globalSetup/2b707dc399d1de87afad.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://resources.usersnap.com/widget-assets/js/chunks/4833/ac3e7dc61ff8256faf1e.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://resources.usersnap.com/widget-assets/js/chunks/widgetApi/0dd8ca38deaf99750771.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/myAccount/account/stats.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' \
  --data-raw 'accountId=**********&selectedAccountIds=**********&numDaysBack=30&action=content_stats' ;
curl 'https://www.dtad.de/workxl/setCookie.do?cookieName=widget_tenders_info_pie_chart_period&cookieValue=30' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/setCookie.do?cookieName=widget_tenders_info_pie_chart_accounts&cookieValue=**********' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/myAccount/widget/profileResultList.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/myAccount/rfq/viewedList.do?dashBoard=1&numDaysBack=60' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/myAccount/widget/profileList.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/setCookie.do?cookieName=widget_profiles_results_period&cookieValue=14' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/myAccount/widget/relevantContentList360.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' \
  --data-raw 'dashBoard=1' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=12&ownerCompanyId=**********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=112&assignedAccountId=**********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/setCookie.do?cookieName=widget_profiles_results_account&cookieValue=**********' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=5&assignedAccountId=**********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=5&assignedAccountId=**********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/myAccount/account/stats.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' \
  --data-raw 'accountId=**********&numDaysBack=14&action=search_profile_stats' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/common/watch_inactive.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/common/rfq_type_public.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/common/rfq_doctype_invitation_to_tender.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/common/icon_isArchiv.png' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=12&ownerCompanyId=**********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-data-proxy?crm_data_action=10&contentId=********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-data-proxy?crm_data_action=10&contentId=********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/servlet/crm-data-proxy?crm_data_action=10&contentId=********&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/common/rfq_type_ted.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://dtad.zendesk.com/frontendevents/pv?client=1B752747-577B-429A-A0E0-83861AF69088' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"url":null,"buid":"9b9414b32c74421a9232c7a908fb1e16","channel":"web_messenger","version":"cd0b9d8","timestamp":"2025-09-04T12:21:46.157Z","suid":"5d1af773461a4f328a6adf927a9b461d","pageView":{"pageTitle":"DTAD Cockpit | DTAD","referrer":null,"time":0,"loadTime":2081,"navigatorLanguage":"de-DE","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","helpCenterDedup":false}}' ;
curl 'https://maps.googleapis.com/maps-api-v3/api/js/61/14/intl/de_ALL/common.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://maps.googleapis.com/maps-api-v3/api/js/61/14/intl/de_ALL/util.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0'
