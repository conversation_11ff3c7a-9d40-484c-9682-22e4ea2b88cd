#!/usr/bin/env python3
import base64
import hashlib
import os
import re
import json
import requests
from urllib.parse import parse_qs, urlparse, urlencode

# Configuration
import sys

# Get credentials from command line arguments if provided
if len(sys.argv) >= 3:
    USERNAME = sys.argv[1]
    PASSWORD = sys.argv[2]
else:
    # Default credentials (should be overridden by command line arguments)
    USERNAME = "your_username"  # Replace with your username
    PASSWORD = "your_password"  # Replace with your password

# URLs
LOGIN_URL = "https://identity.vergabeportal.at/Account/Login"
TOKEN_URL = "https://identity.vergabeportal.at/connect/token"

# Generate PKCE code verifier and challenge
def generate_pkce_pair():
    code_verifier = base64.urlsafe_b64encode(os.urandom(40)).decode('utf-8')
    code_verifier = re.sub('[^a-zA-Z0-9]+', '', code_verifier)

    code_challenge = hashlib.sha256(code_verifier.encode('utf-8')).digest()
    code_challenge = base64.urlsafe_b64encode(code_challenge).decode('utf-8')
    code_challenge = code_challenge.replace('=', '')

    return code_verifier, code_challenge

# Generate a random state
def generate_state():
    return base64.urlsafe_b64encode(os.urandom(16)).decode('utf-8').replace('=', '')

# Extract request verification token from login page
def get_request_verification_token(session):
    response = session.get(LOGIN_URL)
    match = re.search(r'name="__RequestVerificationToken" type="hidden" value="([^"]+)"', response.text)
    if match:
        return match.group(1)
    else:
        raise Exception("Could not find request verification token")

# Main function to authenticate and get bearer token
def authenticate():
    session = requests.Session()

    # Step 1: Generate PKCE code verifier and challenge
    code_verifier, code_challenge = generate_pkce_pair()
    state = generate_state()

    # Step 2: Get request verification token
    request_token = get_request_verification_token(session)
    print(f"Got request verification token: {request_token[:20]}...")

    # Step 3: Prepare the authorization URL with PKCE
    redirect_uri = "https://www.vergabeportal.at/assets/pages/auth-callback.html"
    scope = "openid profile vergabeportal_identity_api eabgabe_api vergabeportal_api tenderdata_api eingabeportal_api emailcommunication_api"

    auth_params = {
        "client_id": "vergabeportal_angular",
        "redirect_uri": redirect_uri,
        "response_type": "code",
        "scope": scope,
        "state": state,
        "code_challenge": code_challenge,
        "code_challenge_method": "S256",
        "response_mode": "query"
    }

    # Build the return URL for the login form

    return_url = f"/connect/authorize/callback?{urlencode(auth_params)}"

    # Step 4: Submit login form
    login_data = {
        "ReturnUrl": return_url,
        "Username": USERNAME,
        "Password": PASSWORD,
        "button": "login",
        "__RequestVerificationToken": request_token
    }

    login_url_with_return = f"{LOGIN_URL}?ReturnUrl={requests.utils.quote(return_url)}"
    print(f"Logging in as {USERNAME}...")

    response = session.post(
        login_url_with_return,
        data=login_data,
        allow_redirects=True
    )

    # Step 5: Extract the authorization code from the final URL
    final_url = response.url
    print(f"Final URL after redirects: {final_url}")

    parsed_url = urlparse(final_url)
    query_params = parse_qs(parsed_url.query)

    if 'code' not in query_params:
        print("Error: Authorization code not found in the response URL")
        print("Response content:", response.text[:500])
        return None

    auth_code = query_params['code'][0]
    print(f"Got authorization code: {auth_code[:10]}...")

    # Step 6: Exchange the authorization code for a token
    token_data = {
        "client_id": "vergabeportal_angular",
        "code": auth_code,
        "redirect_uri": redirect_uri,
        "code_verifier": code_verifier,
        "grant_type": "authorization_code"
    }

    print("Exchanging authorization code for token...")
    token_response = session.post(TOKEN_URL, data=token_data)

    if token_response.status_code != 200:
        print(f"Error getting token: {token_response.status_code}")
        print(token_response.text)
        return None

    token_json = token_response.json()
    access_token = token_json.get("access_token")

    if access_token:
        print(f"Successfully obtained bearer token: {access_token[:15]}...")
        return access_token
    else:
        print("Error: No access token in response")
        print(token_response.text)
        return None

if __name__ == "__main__":
    print("Starting authentication process...")
    access_token = authenticate()
    print("access_token:")
    print(access_token)
