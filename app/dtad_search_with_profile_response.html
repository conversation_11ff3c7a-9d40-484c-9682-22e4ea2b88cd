<tr class="rfqRow" id="rfq23439956" onmouseover="setBackgroundColor('rfq23439956')"
    onmouseout="unsetBackgroundColor('rfq23439956')">


    <td class="list-column1" align="center"><input type="checkbox" name="rfqIds" value="23439956_1"
                                                   id="watch_23439956_1"></td>


    <td class="list-column1">
        <div class="listNumerationColumn" style="position: relative;text-align: left;">
            <span class='listNumeration'>1</span>
            <span class="listAssignedUser" style="display:none;cursor: pointer;" title=""></span>
            <div>
            </div>
        </div>
    </td>


    <td class="list-column1" align="center">

        <div class="crmBox">
            <img class="crmIcon ept-element" data-eptelementname="CRM" id="watch_23439956"
                 onclick="processContentWatchedRequest(23439956,1,2149549668, 1);_gaq.push(['_trackEvent', 'AundA', 'Suchergebnisliste', 'Beobachten gewählt']);"
                 src="/workxl/layout/templates/images/common/watch_inactive.png"
                 alt="Zu Favoriten hinzufügen"
                 title="Zu Favoriten hinzufügen">
        </div>


        <div class="crmBox">

            <img class="crmIcon ept-element" data-eptelementname="CRM" id="contentTaskImg_23439956"
                 src="/workxl/layout/templates/images/common/task_inactive.png"
                 alt="Termin erstellen"
                 title="Termin erstellen">

        </div>

        <script>
            $('contentTaskImg_23439956').observe('click', function (event) {
                var crmType = "PROJECT";
                var contentElements = [{
                    "contentId": 23439956,
                    "contentTypeId": 1,
                    "contentCustomerTypeId": 0
                }];
                vueGlobalObj.getAppointmentModal().showModal(crmType, contentElements, "contentList");
            });
        </script>


        <div class="crmBox">
            <img class="crmIcon ept-element" data-eptelementname="CRM"
                 id="contentActivityImg_23439956"
                 src="/workxl/layout/templates/images/common/icon_activity_inactive.png"
                 alt="Aktivität erstellen"
                 title="Aktivität erstellen">
        </div>

        <script>
            $('contentActivityImg_23439956').observe('click', function (event) {
                var crmType = "PROJECT";
                var contentElements = [{
                    "contentId": 23439956,
                    "contentTypeId": 1,
                    "contentCustomerTypeId": 0
                }];
                vueGlobalObj.getActivityModal().showModal(crmType, contentElements, "contentList");
            });
        </script>


        <div class="crmBox">

            <img class="crmIcon ept-element" data-eptelementname="CRM"
                 data-eptelementvalue="Benachrichtigung hinzugefügt" id="contentAlertImg_23439956"
                 onclick="dataLayer.push({'event': 'customEPTEvent', 'customEPTEventLabel':'Benachrichtigung hinzugefügt'});loadOverlayContentNote(23439956, 'alertPopup', 1,2149549668);"
                 src="/workxl/layout/templates/images/common/alert_inactive.png"
                 alt="Benachrichtigung anlegen"
                 title="Benachrichtigung anlegen">

        </div>
    </td>


    <td class="list-column1">


        <div class=" isFrameContract"
             title=" Zeitvertrag">


            <span class="listAssignedWorkflow" title=""/></span>
            <span id="lnkId-23439956_1-1756991610463"
                  class="listLink"
                  title='Ausschreibung - Bereitstellung und Betrieb eines virtuellen Windows Server in Hamburg  '>
                     Bereitstellung und Betrieb eines virtuellen Windows Server
            </span>

            <br>


            <div id="rfqPrecis23439956" style="display:none">
                <div class="rfq-details-tooltip" style="display:none"></div>
            </div>


        </div>


        <div class="mobile" style="margin-left: 17px; font-size:12px; line-height: 2.5em">
            20459 Hamburg ,
            04.09.2025
        </div>

    </td>


    <td class="list-column1">

        20459 Hamburg


    </td>


    <td class="list-column1">
        04.09.2025
    </td>


    <td class="list-column1">
        30.09.2025
    </td>


    <td class="list-column1" align="center">


        <img src="/workxl/layout/templates/images/common/rfq_type_public.gif"
             alt="Nationale Ausschreibung"
             title="Nationale Ausschreibung"
             border="0">


    </td>

    <td class="list-column1" align="center">


        <img id='23439956_1'
             src="/workxl/layout/templates/images/common/rfq_doctype_invitation_to_tender.gif"
             alt="Ausschreibung"
             title="Ausschreibung">


    </td>


    <td class="list-column1" align="center" onclick="showRfqShortDesc('23439956_1')">
        <div id="icon_desc_23439956_1"
             class="openCloseHtmlIcon">
        </div>
    </td>


</tr>


<tr class="short_desc_23439956_1 rfqDesc">


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>
    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    " colspan="5"></td>
</tr>


<tr class="short_desc_23439956_1 rfqDesc">


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "><b>DTAD-ID:</b></td>
    <td class="td360Desc_1
    ">23439956
    </td>


    <td class="td360Desc_1
    ">


    </td>


    <td class="td360Desc_1
    " colspan="5">


    </td>
</tr>


<tr class="short_desc_23439956_1 rfqDesc">

    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "><b>Auftragsart:</b></td>
    <td class="td360Desc_1
    " colspan="1">
        Nationale Ausschreibung

    </td>


    <td class="td360Desc_1
    ">


        Frist Angebotsabgabe:


    </td>


    <td class="td360Desc_1
    " colspan="5">


        30.09.2025


    </td>
</tr>


<tr class="short_desc_23439956_1 rfqDesc">

    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "><b>Dokumententyp:</b></td>
    <td class="td360Desc_1
    ">


        Ausschreibung


    </td>


    <td class="td360Desc_1
    ">

        Zeitvertrag endet:

    </td>


    <td class="td360Desc_1
    " colspan="5">


        Dezember 2026


    </td>
</tr>


<tr class="short_desc_23439956_1 rfqDesc">

    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    " valign="top"><b>Beschreibung:</b></td>

    <td class="td360Desc_1
    " colspan="7">

        Bereitstellung und Betrieb eines virtuellen Windows Server\<br>\<br>Branchenspezifisches Softwarepaket
        (48100000)\<br>Wartung von Informationstechnologiesoftware (72267100)

    </td>

</tr>


<tr class="short_desc_23439956_1 rfqDesc">


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    " valign="top"><b>Vergabestelle:</b></td>

    <td class="td360Desc_1
    " colspan="7">
        Generalzolldirektion Zentrale Beschaffungsstelle der Bundesfinanzverwaltung Dienstsitz Freiburg
    </td>
</tr>


<tr class="short_desc_23439956_1 rfqDesc">


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    "></td>


    <td class="td360Desc_1
    " valign="top"><b>Erfüllungsort:</b></td>
    <td class="td360Desc_1
    " colspan="7">
        Generalzolldirektion, Referat DIII.B.2, Stubbenhuk 3, 20459 Hamburg.\

    </td>
</tr>


<tr class="short_desc_23439956_1 rfqDesc">

    <td class="td360Desc_1
    " style="border-bottom: 1px solid #e8eff3"></td>


    <td class="td360Desc_1
    " style="border-bottom: 1px solid #e8eff3"></td>


    <td class="td360Desc_1
    " colspan="8" align="right" style="border-bottom: 1px solid #e8eff3; padding:10px 17px">
        <span onclick="dtad360.getDetailsPage('23439956_1', 'Bereitstellung und Betrieb eines virtuellen Windows Server ', dtad360.TabTypes.RFQ_DETAILS, '55521f425c425d4b5e1f')"
              class="link">Detailansicht</span>
        <div style="display:inline-block; height:4px; width:4px; border: 0 solid transparent; border-right: 2px solid #7ca2b5;  border-bottom: 2px solid #7ca2b5;  transform: rotate(-45deg);"></div>
    </td>

</tr>

