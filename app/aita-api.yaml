openapi: 3.0.3
info:
  title: AI Tender Analysis - REST API
  description: Title
  version: 1.0.0

paths:
  /tenders:
    get:
      operationId: getAllTenders
      tags:
        - Tender
      description: Retrieve all existing tenders
      parameters:
        - in: query
          name: page
          required: true
          schema:
            type: integer
        - in: query
          name: size
          required: true
          schema:
            type: integer
        - in: query
          name: sortBy
          required: false
          description: |
            Name of the property the collection results should be sorted by. Only the properties in the enum are
            supported at the moment.
          schema:
            $ref: '#/components/schemas/TenderSortableFieldsDTO'
        - in: query
          name: sortDirection
          required: false
          description: Sort order
          schema:
            $ref: '#/components/schemas/CollectionSortDirectionDTO'
      responses:
        200:
          description: Request was successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TenderPagedDTO"
        400:
          $ref: "#/components/responses/400"
        500:
          $ref: "#/components/responses/500"
    post:
      operationId: createTender
      tags:
        - Tender
      description: Create a new tender
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/TenderCreateDTO"
      responses:
        201:
          description: Tender has been created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TenderDTO"
        400:
          $ref: "#/components/responses/400"
        500:
          $ref: "#/components/responses/500"

  /tenders/create-with-ai:
    post:
      operationId: createTenderWithAI
      tags:
        - Tender
      description: Create a new tender with AI assistance to extract missing fields
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/TenderCreateSimpleDTO"
      responses:
        201:
          description: Tender has been created with AI assistance
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TenderDTO"
        400:
          $ref: "#/components/responses/400"
        500:
          $ref: "#/components/responses/500"

  /tenders/{id}:
    get:
      operationId: getTenderById
      tags:
        - Tender
      description: Retrieve specific tender
      parameters:
        - name: id
          in: path
          description: Tender ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        200:
          description: Tender was found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TenderDTO"
        404:
          $ref: "#/components/responses/404"
        500:
          $ref: "#/components/responses/500"
    put:
      operationId: updateTender
      tags:
        - Tender
      description: Update specific tender
      parameters:
        - name: id
          in: path
          description: Tender ID
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TenderUpdateDTO"
      responses:
        200:
          description: Tender has been successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TenderDTO"
        404:
          $ref: "#/components/responses/404"
        500:
          $ref: "#/components/responses/500"
    delete:
      operationId: deleteTender
      tags:
        - Tender
      description: Delete specific tender
      parameters:
        - name: id
          in: path
          description: Tender ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        204:
          description: Tender has been successfully deleted
        404:
          $ref: "#/components/responses/404"
        500:
          $ref: "#/components/responses/500"

  /tenders/{id}/analyze:
    post:
      operationId: analyzeTender
      tags:
        - Analysis
      description: Trigger Tender analysis
      parameters:
        - name: id
          in: path
          description: Tender ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        200:
          description: Analysis has been successfully completed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnalysisResultDTO"
        404:
          $ref: "#/components/responses/404"
        500:
          $ref: "#/components/responses/500"


  /tenders/{tenderId}/files/{fileId}:
    get:
      operationId: downloadTenderFile
      tags:
        - Tender
      description: Download specific file of a tender
      parameters:
        - name: tenderId
          in: path
          description: Tender ID
          required: true
          schema:
            type: string
            format: uuid
        - name: fileId
          in: path
          description: File ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        200:
          description: File downloaded successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        404:
          $ref: "#/components/responses/404"
        500:
          $ref: "#/components/responses/500"
    delete:
      operationId: deleteTenderFile
      tags:
        - Tender
      description: Delete specific file of a tender
      parameters:
        - name: tenderId
          in: path
          description: Tender ID
          required: true
          schema:
            type: string
            format: uuid
        - name: fileId
          in: path
          description: File ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        204:
          description: Tender file has been successfully deleted
        404:
          $ref: "#/components/responses/404"
        500:
          $ref: "#/components/responses/500"

  /tenders/{tenderId}/files:
    post:
      operationId: addTenderFile
      tags:
        - Tender
      description: Add file to existing Tender
      parameters:
        - name: tenderId
          in: path
          description: Tender ID
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        201:
          description: File has been uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FileMetadataDTO"
        404:
          $ref: "#/components/responses/404"
        500:
          $ref: "#/components/responses/500"

  /config:
    get:
      operationId: getSystemConfiguration
      tags:
        - Config
      description: Retrieve system configuration
      responses:
        200:
          description: Request was successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SystemConfigurationDTO"
        400:
          $ref: "#/components/responses/400"
        500:
          $ref: "#/components/responses/500"
    put:
      operationId: updateSystemConfiguration
      tags:
        - Config
      description: Update system configuration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SystemConfigurationDTO"
      responses:
        200:
          description: Request was successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SystemConfigurationDTO"
        400:
          $ref: "#/components/responses/400"
        500:
          $ref: "#/components/responses/500"

components:
  schemas:
    TenderCreateDTO:
      description: A tender is a formal process where government agencies or public entities invite bids from suppliers or contractors to provide goods, services, or works.
      type: object
      required:
        - title
        - client
        - submissionDate
        - bindingDeadline
        - contractDuration
        - publicationDate
        - questionDeadline
        - winningCriteria
        - weightingPriceQuality
        - deliveryLocation
        - description
        - workflowStatus
      properties:
        title:
          description: Title of the tender
          type: string
        sourceUrl:
          description: Direct link to the original source of the tender
          type: string
        client:
          description: The publisher of the tender
          type: string
        submissionDate:
          description: The latest date and time until the offer can be submitted (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        bindingDeadline:
          description: The period of time during which a bidder is legally bound by its bid
          type: string
        contractDuration:
          description: Duration of the contract
          type: string
        publicationDate:
          description: Date when the tender was published (ISO 8601 date format)
          type: string
          format: date
          example: 2018-03-20
        questionDeadline:
          description: The question deadline is the last datetime, questions can be sent to the client (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        contractValue:
          description: Value of the whole contract, the currency unit is Euro (€)
          type: number
          format: float
        maximumBudget:
          description: The maximum amount of budget, normally listed in "Person-days" (PT)
          type: string
        winningCriteria:
          description: List of criteria we have to deliver to win the tender + optional topics that are relevant (Must have and nice to have criteria)
          type: string
        weightingPriceQuality:
          description: The ratio between price and quality which is well described in the tender.
          type: string
        deliveryLocation:
          description: Location requirements for the delivery of a tender.
          type: string
        description:
          description: Very long general description text
          type: string
        workflowStatus:
          $ref: "#/components/schemas/WorkflowStatus"
        comment:
          description: User comments about the tender
          type: string
        rating:
          description: User rating of the tender from 1 to 5
          type: integer
          minimum: 1
          maximum: 5
        isFavorite:
          description: Flag indicating if the tender is marked as a favorite
          type: boolean
        files:
          description: List of documents that belong to the tender and should be uploaded
          type: array
          items:
            type: string
            format: binary

    TenderCreateSimpleDTO:
      description: Simplified tender creation DTO for AI-assisted tender creation with only essential fields
      type: object
      required:
        - title
        - description
      properties:
        title:
          description: Title of the tender
          type: string
        sourceUrl:
          description: Direct link to the original source of the tender
          type: string
        description:
          description: Description text that will be analyzed by AI to extract missing fields
          type: string
        files:
          description: List of documents that will be analyzed to extract tender information
          type: array
          items:
            type: string
            format: binary

    TenderUpdateDTO:
      description: A tender is a formal process where government agencies or public entities invite bids from suppliers or contractors to provide goods, services, or works.
      type: object
      required:
        - title
        - client
        - submissionDate
        - bindingDeadline
        - contractDuration
        - publicationDate
        - questionDeadline
        - winningCriteria
        - weightingPriceQuality
        - deliveryLocation
        - description
        - workflowStatus
      properties:
        title:
          description: Title of the tender
          type: string
        sourceUrl:
          description: Direct link to the original source of the tender
          type: string
        client:
          description: The publisher of the tender
          type: string
        submissionDate:
          description: The latest date and time until the offer can be submitted (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        bindingDeadline:
          description: The period of time during which a bidder is legally bound by its bid
          type: string
        contractDuration:
          description: Duration of the contract
          type: string
        publicationDate:
          description: Date when the tender was published (ISO 8601 date format)
          type: string
          format: date
          example: 2018-03-20
        questionDeadline:
          description: The question deadline is the last datetime, questions can be sent to the client (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        contractValue:
          description: Value of the whole contract, the currency unit is Euro (€)
          type: number
          format: float
        maximumBudget:
          description: The maximum amount of budget, normally listed in "Person-days" (PT)
          type: string
        winningCriteria:
          description: List of criteria we have to deliver to win the tender + optional topics that are relevant (Must have and nice to have criteria)
          type: string
        weightingPriceQuality:
          description: The ratio between price and quality which is well described in the tender.
          type: string
        deliveryLocation:
          description: Location requirements for the delivery of a tender.
          type: string
        description:
          description: Very long general description text
          type: string
        comment:
          description: User comments about the tender
          type: string
        rating:
          description: User rating of the tender from 1 to 5
          type: integer
          minimum: 1
          maximum: 5
        isFavorite:
          description: Flag indicating if the tender is marked as a favorite
          type: boolean
        workflowStatus:
          $ref: "#/components/schemas/WorkflowStatus"


    TenderDTO:
      description: A tender is a formal process where government agencies or public entities invite bids from suppliers or contractors to provide goods, services, or works.
      type: object
      required:
        - id
        - title
        - client
        - submissionDate
        - bindingDeadline
        - contractDuration
        - publicationDate
        - questionDeadline
        - winningCriteria
        - weightingPriceQuality
        - deliveryLocation
        - description
        - workflowStatus
      properties:
        id:
          description: ID that uniquely identifies this entity
          type: string
        title:
          description: Title of the tender
          type: string
        sourceUrl:
          description: Direct link to the original source of the tender
          type: string
        client:
          description: The publisher of the tender
          type: string
        submissionDate:
          description: The latest date and time until the offer can be submitted (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        bindingDeadline:
          description: The period of time during which a bidder is legally bound by its bid
          type: string
        contractDuration:
          description: Duration of the contract
          type: string
        publicationDate:
          description: Date when the tender was published (ISO 8601 date format)
          type: string
          format: date
          example: 2018-03-20
        questionDeadline:
          description: The question deadline is the last datetime, questions can be sent to the client (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        contractValue:
          description: Value of the whole contract, the currency unit is Euro (€)
          type: number
          format: float
        maximumBudget:
          description: The maximum amount of budget, normally listed in "Person-days" (PT)
          type: string
        winningCriteria:
          description: List of criteria we have to deliver to win the tender + optional topics that are relevant (Must have and nice to have criteria)
          type: string
        weightingPriceQuality:
          description: The ratio between price and quality which is well described in the tender.
          type: string
        deliveryLocation:
          description: Location requirements for the delivery of a tender.
          type: string
        description:
          description: Very long general description text
          type: string
        files:
          type: array
          items:
            $ref: "#/components/schemas/FileMetadataDTO"
        analysisResult:
          $ref: "#/components/schemas/AnalysisResultDTO"
        creationTime:
          description: The point in time when the object has been created (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        createdBy:
          $ref: "#/components/schemas/UserDTO"
        lastUpdatedTime:
          description: The point in time when the object has been last updated (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        lastUpdatedBy:
          $ref: "#/components/schemas/UserDTO"
        comment:
          description: User comments about the tender
          type: string
        rating:
          description: User rating of the tender from 1 to 5
          type: integer
          minimum: 1
          maximum: 5
        isFavorite:
          description: Flag indicating if the tender is marked as a favorite
          type: boolean
        status:
          $ref: "#/components/schemas/TenderStatusDTO"
        workflowStatus:
          $ref: "#/components/schemas/WorkflowStatus"

    UserDTO:
      description: A short version of user information
      type: object
      properties:
        userId:
          description: Unique ID identifying the user
          type: string
        fullName:
          description: Full name of the user
          type: string

    ErrorDTO:
      description: A response object that provides details about a failed request
      type: object
      properties:
        errorMessage:
          description: An error message that provides general details about the error
          type: string
        details:
          description: An optional list of detailed information about the error that occurred
          type: array
          items:
            format: string

    PagingDTO:
      description: Paging information for a response that returns collection results
      type: object
      required:
        - totalElements
        - totalPages
        - pageNumber
        - pageSize
      properties:
        totalElements:
          description: Total number of elements available
          type: integer
          format: int64
        totalPages:
          description: Total number of pages available
          type: integer
        pageNumber:
          description: Number of page sent in this request
          type: integer
        pageSize:
          description: Number of elements on this page
          type: integer

    TenderPagedDTO:
      description: Paginated response containing an array of TenderDTO and paging info
      type: object
      required:
        - data
        - paging
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/TenderDTO'
        paging:
          $ref: '#/components/schemas/PagingDTO'

    TenderSortableFieldsDTO:
      description: The name of the properties a collection of tenders can be sorted by
      type: string
      enum:
        - contractValue
        - creationTime
        - lastUpdatedTime

    CollectionSortDirectionDTO:
      description: Enum that describes the sort direction of a sorted collection result.
      type: string
      enum:
        - asc
        - desc

    TenderStatusDTO:
      description: Enum that describes the status of a tender.
      type: string
      enum:
        - New
        - Analyzing
        - Analyzed
        - Bid-Analyzed
        - Closed

    WorkflowStatus:
      description: Enum that describes the workflow status of a tender.
      type: string
      enum:
        - New
        - In-Work
        - Declined
        - Offered
        - Done


    FileMetadataDTO:
      description: A file object that belongs to a tender
      type: object
      required:
        - id
        - fileName
        - fileSizeBytes
      properties:
        id:
          description: Unique file id
          type: string
        fileName:
          description: File name
          type: string
        fileSizeBytes:
          description: File size in kilobyte (kB)
          type: integer
          format: int64

    SystemConfigurationDTO:
      description: An object that describes all configurable system properties
      type: object
      required:
        - aiAnalysisSystemPrompt
        - aiAnalysisAnalysisPrompt
        - aiStructuredOutputPrompt
      properties:
        aiAnalysisSystemPrompt:
          description: System prompt that is used for AI-based analysis of Tender data
          type: string
        aiAnalysisAnalysisPrompt:
          description: Prompt that is used for AI-based analysis of Tender data
          type: string
        aiStructuredOutputPrompt:
          description: Prompt that is used for AI-based structured data extraction from Tender information
          type: string

    AnalysisResultDTO:
      description: A response object that provides details the analysis of a tender
      type: object
      properties:
        analysisResult:
          description: A result of the Tender analysis
          type: string
        lastUpdatedTime:
          description: The point in time when the last analysis has been done (ISO 8601 date time format)
          type: string
          format: date-time
          example: 2018-03-20T09:12:28Z
        promptTokens:
          description: Amount of tokens that have been used for the prompt to perform the analysis
          type: number
          format: integer
        completionTokens:
          description: Amount of tokens that have been used for the generation of the result to perform the analysis
          type: number
          format: integer

  responses:
    400:
      description: In case there is an issue with the client input
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorDTO"
    404:
      description: The requested resource could not be found
    500:
      description: In case there was an issue on server side while processing the request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorDTO"