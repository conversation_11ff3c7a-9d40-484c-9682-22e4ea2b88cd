curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/layout/templates/images/dtad_ladegrafik_animation_a_02.gif' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/myAccount/profile/execute.do' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw 'id=**********&360inline=1' ;
curl 'https://widget.usersnap.com/embed/load/271b77e9-2764-4598-bafa-5bdf249d2e2b?onload=onUsersnapCXLoad' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: script' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://www.dtad.de/workxl/360/checkSessionAlive.do' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-length: 0' \
  -H 'content-type: application/x-www-form-urlencoded; charset=utf-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://resources.usersnap.com/widget-assets/js/entries/globalSetup/2b707dc399d1de87afad.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://resources.usersnap.com/widget-assets/js/chunks/4833/ac3e7dc61ff8256faf1e.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://resources.usersnap.com/widget-assets/js/chunks/widgetApi/0dd8ca38deaf99750771.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/myAccount/account/saveTabs.do' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' \
  --data-raw '{"activeViewId":"searchProfil_all_**********","views":[{"id":"profileList","name":"Suchprofile","tabType":13,"url":"/workxl/myAccount/profile/list.do","scrollOffset":0},{"id":"searchProfil_all_**********","name":"Ergebnisse Ihres Suchprofils Software, Coaching und UX","tabType":6,"url":"/workxl/myAccount/profile/execute.do","parameters":"id=**********&360inline=1","scrollOffset":0},{"id":"rfqSelectedSetId_********","name":"Suchprofilergebnisse vom Montag, 03.03.2025","tabType":6,"url":"/workxl/myAccount/rfq/selectedList.do","parameters":"rfqSelectedSetId=********&inline=1","scrollOffset":0},{"id":"********_1","name":"Rahmenvertrag zur Begleitung bei der Organisations- und Strategieentwicklung  in Berlin  (ID:********)","tabType":3,"url":"/workxl/rfq/details.do","parameters":{"360inline":true,"identifier":"********_1"},"scrollOffset":0,"altTitle":"Ausschreibung - Rahmenvertrag zur Begleitung bei der Organisations- und Strategieentwicklung"},{"id":"editProfile_**********","name":"Suchprofil bearbeiten","tabType":17,"url":"/workxl/myAccount/profile/edit.do","parameters":{"id":"**********","name":"Software, Coaching und UX","pattern":"Agile Software Development Entwicklung UX Design Cloud Beratung Migration Individualsoftware ","patternConnection":"1","categoryIds":["c2117","c2325","c2333"],"baseZip":"","baseCountryId":"276","rfqTypeIds":"1","typeOfDocuments":"invitation_to_tender","360inline":1,"showSectors":"1","testImage.x":"0","testImage.y":"0"},"scrollOffset":0},{"id":"profileTest","name":"Suchprofil testen","tabType":1,"url":"/workxl/myAccount/profile/editValidate.do?showSectors=1","parameters":{"id":"**********","name":"Software, Coaching und UX","pattern":"Agile Software Development Entwicklung UX Design Cloud Beratung Migration Individualsoftware ","patternConnection":"1","categoryIds":["c2117","c2325","c2333"],"baseZip":"","baseCountryId":"276","rfqTypeIds":"1","typeOfDocuments":"invitation_to_tender","360inline":1,"showSectors":"1","testImage.x":"0","testImage.y":"0"},"scrollOffset":14116},{"id":"22965223_1","name":"Externes Projektmanagement und inhaltliche Steuerung für das Interreg Europe Projekt FLAVOR","tabType":3,"url":"/workxl/rfq/details.do","parameters":{"360inline":true,"identifier":"22965223_1","rfqListCriteriaKey":"5d5d17425c435d495c"},"scrollOffset":0,"altTitle":"Ausschreibung - Externes Projektmanagement und inhaltliche Steuerung für das Interreg Europe Projekt FLAVOR in Eisenstadt "},{"id":"22929936_1","name":"Digitales Abstimmungssystem für die Sitzungen des Rates der Landeshauptstadt Düsseldorf","tabType":3,"url":"/workxl/rfq/details.do","parameters":{"360inline":true,"identifier":"22929936_1","rfqListCriteriaKey":"55571a405c44534d5c1b"},"scrollOffset":0,"altTitle":"Ausschreibung - Digitales Abstimmungssystem für die Sitzungen des Rates der Landeshauptstadt Düsseldorf in Düsseldorf  "},{"id":"4040061c6c1d5c720113","name":"Stadt Düsseldorf Rechtsamt Zentrale Vergabestelle (Amt 30/41)","tabType":4,"url":"/workxl/fuv/details.do","parameters":{"360inline":true,"companyId":"4040061c6c1d5c720113"},"scrollOffset":0},{"id":"23091652_1","name":"Unterstützungsdienstleistungen im Rahmen der IT-Betriebskonsolidierung","tabType":3,"url":"/workxl/rfq/details.do","parameters":{"360inline":true,"identifier":"23091652_1"},"scrollOffset":0,"altTitle":"Ausschreibung - Unterstützungsdienstleistungen im Rahmen der IT-Betriebskonsolidierung"},{"id":"search","name":null,"tabType":9,"url":"/workxl/rfq/searchExpert360.do","scrollOffset":0}],"expertSearchFormCache":{"formId":"searchForm","formInputs":{"ga_formId":"rfq_suche_expert","ga_searchCategory":"Auftragsinformationen","pattern":"********","patternConnection":"0","baseCountryId":"276","regionIds":"DE","publicationDays":"60"},"openBoxElements":[]}}' ;
curl 'https://resources.usersnap.com/widget-assets/js/entries/widgetAppEmbed/1d8e64cef3635701e80e.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.dtad.de/workxl/servlet/crm-proxy?crm_api_action=13&rfqIds=23439956%2C23440102%2C23440124%2C23440521%2C23440680%2C23440717%2C23440825%2C23440844%2C23440925%2C23434010%2C23434044%2C23434067%2C23434274%2C23434698%2C23435019%2C23435860%2C23435891%2C23435902%2C23435906%2C23436173%2C23436229%2C23429238%2C23429312%2C23429446%2C23429468%2C23430017%2C23430038%2C23430059%2C23430175%2C23421068%2C23424224%2C23424225%2C23424257%2C23424296%2C23424314%2C23424327%2C23424337%2C23424642%2C23425122%2C23425127%2C23425133%2C23425144%2C23425145%2C23419194%2C23419370%2C23419450%2C23419854%2C23420106%2C23420266%2C23420538&_cb=*************' \
  -H 'accept: text/javascript, text/html, application/xml, text/xml, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -b 'JSESSIONID=A2AF6D75DE6D60FFF720B967C86FED53; widget_profiles_results_account=**********; widget_tenders_info_pie_chart_accounts=**********; widget_profiles_results_period=14; widget_tenders_info_pie_chart_period=30; CookieConsent={stamp:%27y1kmZUgHvUR5Y8ytGZIhSH+3V53xDACsowOMQhV+DuZ5lZSSaOjCNw==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:*************%2Cregion:%27at%27}; usetiful-visitor-ident=f72ed20b-ef68-4c3d-3a85-562e610ead18; savedLoginAccount=137f67147744556461092a34114d13531e1e60707a0e464c2a054d0050534546741a2c43725f045e434f07' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-prototype-version: 1.7.3' \
  -H 'x-requested-with: XMLHttpRequest' ;
curl 'https://resources.usersnap.com/widget-assets/js/chunks/widgetComponents_en-json/6b4bb159d7061c6fef2e.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://resources.usersnap.com/widget-assets/js/chunks/widgetEmbed_en-json/76f7b459bbbd8a1ddd7c.js' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'Referer;' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://widget.usersnap.com/track/271b77e9-2764-4598-bafa-5bdf249d2e2b/open' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://dtad.zendesk.com/frontendevents/pv?client=1B752747-577B-429A-A0E0-83861AF69088' \
  -H 'accept: */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'content-type: application/json' \
  -H 'origin: https://www.dtad.de' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"url":null,"buid":"9b9414b32c74421a9232c7a908fb1e16","channel":"web_messenger","version":"cd0b9d8","timestamp":"2025-09-04T12:24:48.123Z","suid":"5d1af773461a4f328a6adf927a9b461d","pageView":{"pageTitle":"Ergebnisse Ihres Suchprofils Software, Coaching und UX | DTAD","referrer":null,"time":0,"loadTime":184048,"navigatorLanguage":"de-DE","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","helpCenterDedup":false}}'
