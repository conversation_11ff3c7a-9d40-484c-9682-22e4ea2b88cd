search request:

curl 'https://gateway.vergabeportal.at/api/v1/td/client/tender/search' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7' \
  -H 'authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjE5NjhCMzA4RTVBRjk5QUNGMzAwQTg4MTExNDUwNDlFODhBRUFEQThSUzI1NiIsInR5cCI6ImF0K2p3dCIsIng1dCI6IkdXaXpDT1d2bWF6ekFLaUJFVVVFbm9pdXJhZyJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DTW8aLcdADxEsCqh-LIXOXbS6Nuvh1NNTrt5i9_lPmDidkClahDOKJAAJ9ekqcTwkXb6Z-UmoXrN74zCIg9zXOSAMiLb8VF9uapQ3L6oEMxh7se-ugLyjbT1nDdlcQyXO2we6-u0H-WGcLgy_zxqSu7FMP_4b5pYZqc5w1lEJUmdqHKq5VbJZzcbUA-nMjsgd-9Dhs2LKs2BZMmpEU9Uky5GNmhZamuNCOLNf57Er5gKhcS4GBl_Y8Fi5BBg6uFhSSxXh3TA2wTg_Gybo7wWeR_XRUH47ZwYC4x_k7YwFqsdWyh6Jc26qPhJVoeq9Zwaq40ky8vnUtC7xMS4hOH0lw' \
  -H 'content-type: application/json' \
  -b '__hs_cookie_cat_pref=1:false_2:false_3:false; lang=de; NSC_mc_hbufxbz.wfshbcfqpsubm.bu=ffffffff0902147545525d5f4f58455e445a4a42f2d1' \
  -H 'mandanttoken: AT' \
  -H 'origin: https://www.vergabeportal.at' \
  -H 'priority: u=1, i' \
  -H 'referer: https://www.vergabeportal.at/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36' \
  --data-raw '{"pagination":{"currentPage":1,"pageSize":10},"criteria":{"searchWords":"","excludeWords":null,"contractingAuthority":null,"contractTypes":["contract_type_services","contract_type_competition"],"searchMethod":0,"regions":["Country_AT","Region_AT_11","Region_AT_12","Region_AT_13","Region_AT_21","Region_AT_22","Region_AT_31","Region_AT_32","Region_AT_33","Region_AT_34"],"formMainTypes":["notice_main_prior_information_notice","notice_main_current_tenders","notice_main_correction"],"publishDateType":60,"publishDateFrom":null,"publishDateTo":null,"submitDateType":0,"submitDateFrom":null,"submitDateTo":null,"cpvCodes":["72000000","72210000","72211000","72212000","72212100","72212140","72212150","72212160","72212200","72212220","72212222","72212300","72212311","72212312","72212400","72212412","72212430","72212442","72212443","72212445","72212451","72212500","72212771","72212920","72212960","72212982","72220000","72221000","72222300","72224000","72224100","72224200","72227000","72230000","72231000","72232000","72240000","72241000","72242000","72243000","72244000","72245000","72246000","72260000","72262000","72263000","72264000","72265000","72266000","72267000","72267100","72267200","72268000","72420000","72421000","72422000"],"thresholdType":null,"procedureTypes":[],"filterContractingAuthorities":[],"filterCpvCodes":[],"filterProcedureTypes":[],"filterRegions":[],"filterFormMainTypes":[]},"sort":{"label":"publishDate_desc","value":4}}'

response:

[
    {
        "id": "ba2dd52a-c5f9-4aa7-a910-c23f771d4b87",
        "source": null,
        "formType": "notice_contract",
        "formMainType": "notice_main_current_tenders",
        "contractType": "contract_type_services",
        "procedureType": "procedure_type_direct_award_with_notice",
        "thresholdType": null,
        "docNumber": "2025-577087",
        "title": "Software für Energiemonitoring",
        "shortDescription": "Das Ausschreibungsziel des vorliegenden Vergabeverfahrens besteht in der Beschaffung einer leistungsfähigen Softwarelösung zur Überwachung, Analyse und Visualisierung von Energie- und Messdaten, welche die Anforderungen an ein modernes Energiemanagementsystem gemäß ISO 50001 erfüllt.",
        "content": null,
        "submitDeadline": "2025-09-02T08:00:00Z",
        "openingDate": null,
        "publishedAt": "2025-08-01T07:18:40Z",
        "tenderDocumentation": null,
        "procurementUrl": "https://kabeg.vemap.com/home/<USER>/anzeigen.html?annID=685",
        "documentationValue": null,
        "documentationValueInfo": null,
        "regions": [
            {
                "regionCode": "Region_AT_21",
                "areaCode": "Austria"
            }
        ],
        "cpvCodes": [
            "<mark>72260000</mark>"
        ],
        "contractingAuthorities": [
            {
                "name": "Landeskrankenanstalten-Betriebsgesellschaft - KABEG"
            }
        ]
    },
    {
        "id": "6c8aabe1-74d8-4c4a-8f33-022ab99d51f6",
        "source": null,
        "formType": "notice_contract",
        "formMainType": "notice_main_current_tenders",
        "contractType": "contract_type_services",
        "procedureType": "procedure_type_competitive_procedure_without_prior_notice",
        "thresholdType": null,
        "docNumber": "2025-556994",
        "title": "Erweiterung Online Services für die Aktenverwaltung V-DOK",
        "shortDescription": "Das Land Vorarlberg sowie die Gemeinden Vorarlbergs nutzen gemeinsam das Aktenverwaltungssystem V-DOK, eine vorarlbergspezifische Lösung welche auf die Fabasoft-eGov-Suite aufgesetzt ist. Zur Erweiterung dieses Systems um Online-Funktionalitäten (Formularbereitstellung, Serviceportal-Anbindung, öffentliche Bereitstellung von Dokumenten etc.) sollen die Fabasoft Online-Services in Form einer Erweiterung des Bestandsystems zum Einsatz kommen. Diese sind technisch eng mit der bestehenden eGov-Suite und weiteren Fabasoft-Komponenten verzahnt (Rechtemodell, Workflows, Portlet in Liferay, DMZ-Instanz, etc.).",
        "content": null,
        "submitDeadline": null,
        "openingDate": null,
        "publishedAt": "2025-07-26T18:35:01.6621118+02:00",
        "tenderDocumentation": null,
        "procurementUrl": null,
        "documentationValue": null,
        "documentationValueInfo": null,
        "regions": [
            {
                "regionCode": "Region_AT_34",
                "areaCode": "Austria"
            }
        ],
        "cpvCodes": [
            "<mark>72265000</mark>",
            "<mark>72267100</mark>",
            "<mark>72268000</mark>",
            "48310000",
            "48312000"
        ],
        "contractingAuthorities": [
            {
                "name": "Land Vorarlberg"
            }
        ]
    }
]
