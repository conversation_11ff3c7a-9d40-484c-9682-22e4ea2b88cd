services:
  mongodb:
    image: mongo:7.0.15
    ports:
      - 27017:27017
    healthcheck:
      test: ["C<PERSON>", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s
    environment:
      ALLOW_EMPTY_PASSWORD: "yes"
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: aita-tender

#  mongodb:
#    image: bitnami/mongodb:7.0.15
#    ports:
#      - 27017:27017
#    healthcheck:
#      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
#      interval: 10s
#      timeout: 5s
#      retries: 5
#      start_period: 5s
#    environment:
#      ALLOW_EMPTY_PASSWORD: "yes"
#      MONGODB_USERNAME: admin
#      MONGODB_PASSWORD: password
#      MONGODB_DATABASE: aita-tender

#  web:
#    image: craita.azurecr.io/aita-backend:latest
#    depends_on:
#      mongodb:
#        condition: service_healthy
#    ports:
#      - 8080:8080
#    environment:
#      SPRING_DATA_MONGODB_HOST: mongodb
#      SPRING_DATA_MONGODB_USERNAME: admin
#      SPRING_DATA_MONGODB_PASSWORD: password
