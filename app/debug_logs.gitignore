Starting programmatic OAuth2 authorization for Vergabeportal
Starting programmatic Vergabeportal login
Generated PKCE parameters - code_challenge: cv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU, state: LDK8eTRUpRxzWapDWx1olg
Return URL: /connect/authorize/callback?client_id=vergabeportal_angular&redirect_uri=https://www.vergabeportal.at/assets/pages/auth-callback.html&response_type=code&scope=openid%20profile%20vergabeportal_identity_api%20eabgabe_api%20vergabeportal_api%20tenderdata_api%20eingabeportal_api%20emailcommunication_api&state=LDK8eTRUpRxzWapDWx1olg&code_challenge=cv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU&code_challenge_method=S256&response_mode=query
Captured session cookies: .AspNetCore.Antiforgery.yq52FbUrNDY=CfDJ8Bg4LAJ2aXVMk3_MELMRqDbp7RrR6Lf_Xmdfhsofex4av1ul08T6aS4n6V5L...
Got CSRF token: CfDJ8Bg4LAJ2aXVMk3_M...
Including session cookies in form submission
Login URL with ReturnUrl: https://identity.vergabeportal.at/Account/Login?ReturnUrl=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3Dvergabeportal_angular%26redirect_uri%3Dhttps%3A%2F%2Fwww.vergabeportal.at%2Fassets%2Fpages%2Fauth-callback.html%26response_type%3Dcode%26scope%3Dopenid%2520profile%2520vergabeportal_identity_api%2520eabgabe_api%2520vergabeportal_api%2520tenderdata_api%2520eingabeportal_api%2520emailcommunication_api%26state%3DLDK8eTRUpRxzWapDWx1olg%26code_challenge%3Dcv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU%26code_challenge_method%3DS256%26response_mode%3Dquery
Form submission response status: 200 OK
Updated session cookies after login (length: 3298)
No Location header, checking response body (length: 5660)
Extracted meta refresh URL: https://identity.vergabeportal.at/connect/authorize/callback?client_id=vergabeportal_angular&redirect_uri=https://www.vergabeportal.at/assets/pages/auth-callback.html&response_type=code&scope=openid%20profile%20vergabeportal_identity_api%20eabgabe_api%20vergabeportal_api%20tenderdata_api%20eingabeportal_api%20emailcommunication_api&state=LDK8eTRUpRxzWapDWx1olg&code_challenge=cv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU&code_challenge_method=S256&response_mode=query
Making GET request to callback URL to get authorization code
Decoded callback URL: https://identity.vergabeportal.at/connect/authorize/callback?client_id=vergabeportal_angular&redirect_uri=https://www.vergabeportal.at/assets/pages/auth-callback.html&response_type=code&scope=openid profile vergabeportal_identity_api eabgabe_api vergabeportal_api tenderdata_api eingabeportal_api emailcommunication_api&state=LDK8eTRUpRxzWapDWx1olg&code_challenge=cv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU&code_challenge_method=S256&response_mode=query
Trying callback request with original URL: https://identity.vergabeportal.at/connect/authorize/callback?client_id=vergabeportal_angular&redirect_uri=https://www.vergabeportal.at/assets/pages/auth-callback.html&response_type=code&scope=openid%20profile%20vergabeportal_identity_api%20eabgabe_api%20vergabeportal_api%20tenderdata_api%20eingabeportal_api%20emailcommunication_api&state=LDK8eTRUpRxzWapDWx1olg&code_challenge=cv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU&code_challenge_method=S256&response_mode=query
Request headers: [User-Agent:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36", Accept-Language:"de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7", sec-ch-ua:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"", sec-ch-ua-mobile:"?0", sec-ch-ua-platform:""macOS"", sec-fetch-dest:"document", sec-fetch-mode:"navigate", sec-fetch-site:"same-origin", upgrade-insecure-requests:"1", Referer:"https://identity.vergabeportal.at/Account/Login", Cookie:".AspNetCore.Antiforgery.yq52FbUrNDY=CfDJ8Bg4LAJ2aXVMk3_MELMRqDbp7RrR6Lf_Xmdfhsofex4av1ul08T6aS4n6V5LPufN2P_FS1XeoAfFffrY3lFFpR7x6r2Pe68dkGD1Gku_BkUF5bS_rlsseSveFrvqlTAokgx6X1Ayph4sc34rUmf5yNE; path=/; samesite=strict; httponly; .AspNetCore.Mvc.CookieTempDataProvider=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; samesite=lax; httponly; idsrv.session=3311FC76EF1DF0891FB2C5A1E06F127D; path=/; secure; samesite=none; .AspNetCore.Identity.Application=CfDJ8Bg4LAJ2aXVMk3_MELMRqDZnH7ip3fU_vqUl0c8_Q-HhN9PyrvQyAN5bT0kdrdy0ZjL7nM3JYux8PYe4xkO-RcoDOThc_NWlrqWyZVBDSX8iD0zSioLQhl98bPZWROvYgKysRDht_alUuARY8g4IPQkeXUqZFwkKNKRJwEehTvwWyNrOT51OP_d4LEzvrhqOtvCibji24aIh_kkO33jKC8nVPPp3hmO5HiLa02YZ89Dv6SgJ4UIxfcomis_UeVOywvnqfio5B9jJEqxiQpBbkenMX6Jnyl74NJFe1H162Yj7K4gkpkmLShuz3rT0TG6illat0m-Vszcb8NHmKQ7kNKXJssZOZeHOEr_I0djqEbWver6_YB7CM_T2Hkh3ZVpySQ5A0yJzPxii4UIWh-acV6qnF-YpLJ7uF-nHSsnOXtPdwm_Jqr6FR0-fs_Zdd67-xU3iO2gd2VkIOvCOZU251VywdezQEPXVY9bnT8h0P_WjFQ3DIxnW7IWEsi08DEOvr51IWLnDiyIsWlqPrfIbo6sp7KqkVRjMdKeYEP545a85Gq0xCcldCv72Sv5lfju6OBA4eq5Vs6qF93iPTlXuCc4zZU0F62MZr2SFELQQvioEQwJlPULcb-nGmvK--5jagVBw-Jjxf6h1pghgmnfgC9qxokr9M95BYTtF2bAJ00hmt3BVfp1I1DlkfW7k25Qu9-vnV0MlEXW5-IYYNy77uOgYT_w-zIIApjdIzJjG3e2YzgMuBRyyFNRz9h3Hirb6o1DF-uSieVzm4l6qI4OVPJHgd7N7iu2EOCGaTyLdsZG0XQeZwDogN5P3T3afx3tcxIRwEZNiWwM6g2oWcwfjFOiVJmNYeMmPOaU6_oxMrl3_zey7rwUb5sj51N_Y8yQN7obzfCrs9tsc9ws8XsZaHYY_gj0Dc1-pdLkEzMa7GHazf7vgEdCrrDROJXhKl6VdjpJCI-FKVa79-dTHBa9yJ9RFqoWTI8ivyX3nGUfZMMmnpLx0-nMDZltk38YqpgN_kewlJGp7e0aifv1uFPr6jTCXRq52hl2E_BmpcZQY0g2u1l4i44NPTJxRICjOCOs7G1pqGItyZPZNVMdpjvkb-2RSy1QHyZqGqYK-kF-vw4HsJ-L6VtGAYjqmDPaIpg4sa-0ctPkvpDTQwClROWOnu5O1s4steJZ47-NsdQhUlMXzxqm5ujvjDMpgZhqzzA21HWPo_WLooDpcCStHVt4iJYHUXNfIew-TuKAb6wnp5nBSr_RRiXIi-f7il167NtpGVA; expires=Mon, 18 Aug 2025 08:22:14 GMT; path=/; secure; samesite=none; httponly; idsrv.session=5878C57F296FDB650B861423E2905494; path=/; secure; samesite=none; .AspNetCore.Identity.Application=CfDJ8Bg4LAJ2aXVMk3_MELMRqDYBNzkHfNaoA39u5em-KXIah11oHm9eI0WNP7FaDeZbJUII-unSRG4QPko-34zOsSWg1qq8LlULdFdgWiq3HCzuzI_HHqxgrORaK6AF8Owa3Bt69s39Bmp5nuZbRhHJhRXODwm9dcPBYwtvzOHLxeckxOasOtREVs-iOctrb9vPjfwV5P6N3MXPVTv7qAd40lw45YybYV-rmlBhQE536euajj6u3stJ2yEZ6TZCdeniDKyTHxXedzPvBzWYEhVR6qa0QSYm3jEf0JKdM8mg5AbwrcAXOa5gf3PJ9cmTkKOCTI-yG7P7mNgTbB2g4yB0BoOYqEcFtQ8ph2V6Zq3i7dCCGyfcIcX5FPiGu3cLZzuvJUjBfTHxdsFrG7oHc-geIygywcGC7ubo2GSGgGHkLRIQxi7mNCM5TnCA9IrxZ-sCMaAWdczOPJw_h3MpFQfs6ZeM8Ov2oWYmEbNYaVW61ZzYdWO096TQ7RLB3dx1VUzCzAhb9E9RV08Ix6sGk01E9hWpRagogTRkWtknKLcjP6TrNtJmjXpQS58BLS9Av8nfnJMmUAK8cO0fycL-udvtOaV2-NnSHE30wDZZi87p8SWqTzB6s46xphBxFQEBM3QxYTwC_8HyWXk_DqGk_dtbqTj-cUQlhU7dVL7WQkMtE8od53BKDCL9xlQEGeQ9fNSSmmDZ1zS8MR1-SZ_Ye_tS7VM5R9bq1IHxQlOspyfNPvtQWZrUefUa2rsFXWUEmhuGAvCjZXyD2H5aM2TElEXWJ5HUc3YKZPEW2OZXMW7Bo6ZDQBypldos3JyX8W7lSmp7SZXaHuz9Po_e6grT8Ho0dZgwgDPoz9A3722zNV3IgzIGfnqa-nniyV1JYZLHWvAb2QvufuNYx5ULqQeDHR_i1RoMhoWWVkhjU38fy2kdQWCEn3PuVm3QLCC3Inh3qtle7y6SWX58AEgf1uT19dSpnW9DB0J6mEM20NzX2bpS09KEpQK_KSFDSRc1UKuuh4NLy1rjNlg21cM-z_z0GPnO6ou8SS1GyER_ST12jOVbluJy_F_YhAIMiNGvntjMyIy2_94GnTxjkS2f-ZplPfL1mfY1ZDMwKtzLBLpidztPXdL1BKYaNlxA15Ih7nK1Jb0hg7xZUupS5pbDD7xckEIzVeP6ESqt1Q59h4zYAmhIReRNYfixWMqe81jx28S0CgyUYKarBQl1IEISlpxw29EZ2dVNVsHrrFoKHH23wC6bqAJkEXpA_yHZew4guTW0Coi3HQ; expires=Mon, 04 Aug 2025 09:07:14 GMT; path=/; secure; samesite=none; httponly"]
Session cookies being sent (first 200 chars): .AspNetCore.Antiforgery.yq52FbUrNDY=CfDJ8Bg4LAJ2aXVMk3_MELMRqDbp7RrR6Lf_Xmdfhsofex4av1ul08T6aS4n6V5LPufN2P_FS1XeoAfFffrY3lFFpR7x6r2Pe68dkGD1Gku_BkUF5bS_rlsseSveFrvqlTAokgx6X1Ayph4sc34rUmf5yNE; path=/;...
Callback response status: 200 OK
Callback response body length: 5178
Callback request successful but no authorization code found in response body
Trying callback request WITHOUT session cookies as experiment
No-cookie response status: 200 OK
Final URL after all redirects: https://identity.vergabeportal.at/connect/authorize/callback?client_id=vergabeportal_angular&redirect_uri=https://www.vergabeportal.at/assets/pages/auth-callback.html&response_type=code&scope=openid%20profile%20vergabeportal_identity_api%20eabgabe_api%20vergabeportal_api%20tenderdata_api%20eingabeportal_api%20emailcommunication_api&state=LDK8eTRUpRxzWapDWx1olg&code_challenge=cv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU&code_challenge_method=S256&response_mode=query
Login form submission failed: Could not extract authorization code from URL: https://identity.vergabeportal.at/connect/authorize/callback?client_id=vergabeportal_angular&redirect_uri=https://www.vergabeportal.at/assets/pages/auth-callback.html&response_type=code&scope=openid%20profile%20vergabeportal_identity_api%20eabgabe_api%20vergabeportal_api%20tenderdata_api%20eingabeportal_api%20emailcommunication_api&state=LDK8eTRUpRxzWapDWx1olg&code_challenge=cv40kfix7VfbFu7XPoLedgZs3smIbhvRrLXVFWAuuyU&code_challenge_method=S256&response_mode=query
